name: bushy
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  flutter_smart_dialog: ^4.9.8+5
  extended_image: ^10.0.1
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.10+1
  easy_refresh: ^3.4.0
  wechat_assets_picker: ^9.2.0
  scroll_to_index: ^3.0.1
  loading_animation_widget: ^1.3.0
  photo_view: ^0.15.0
  get: ^4.6.6
  get_storage: ^2.1.1
  logger: ^2.4.0
  connectivity_plus: ^6.1.0
  uuid: ^4.5.1
  device_info_plus: ^11.1.0
  package_info_plus: ^8.1.0
  video_player: ^2.9.2
  url_launcher: ^6.3.1
  android_id: ^0.4.0
  permission_handler: ^12.0.0+1
  speech_to_text: ^7.0.0
  audioplayers: ^6.1.0
  vibration: ^3.1.3
  fast_rsa: ^3.6.6
  flutter_image_compress: ^2.3.0
  adjust_sdk: ^5.0.4
  app_tracking_transparency: ^2.0.6
  firebase_core: ^3.8.0
  firebase_remote_config: ^5.1.5
  firebase_analytics: ^11.4.0
  in_app_purchase: ^3.2.1
  flutter_secure_storage: ^9.2.2
  google_fonts: ^6.2.1
  flutter_spinkit: ^5.2.1
  dio: ^5.7.0
  animated_text_kit: ^4.2.2
  lottie: ^3.2.0
  loading_indicator: ^3.1.1
  flutter_staggered_grid_view: ^0.7.0

  flutter_client_sse:
    git:
      url: https://github.com/moritz157/flutter_client_sse.git

  shimmer: ^3.0.0
  shared_preferences: ^2.5.3
  google_ml_kit: ^0.20.0
  hive: ^2.2.3


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.4.13
  flutter_gen_runner: ^5.8.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/new_ui/
    - assets/lottie/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
