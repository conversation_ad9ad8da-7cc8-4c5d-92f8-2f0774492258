<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>bushy</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>bushy</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FacebookAppID</key>
	<string></string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<true/>
	<key>FacebookClientToken</key>
	<string></string>
	<key>FacebookCodelessDebugLogEnabled</key>
	<true/>
	<key>FacebookDisplayName</key>
	<string>bushy</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSLocalNetworkUsageDescription</key>
	<string>The app requires your consent to access the local network</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We need access to your microphone for speech recognition during calls.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow access to the photo album, enabling users to upload pictures to change the chat background.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>We need access to speech recognition for voice input during calls.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>The app requires your consent in order to provide you with login device protection</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
