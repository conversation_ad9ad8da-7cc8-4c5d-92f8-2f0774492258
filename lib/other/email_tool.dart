import 'package:bushy/common/language_checker.dart';
import 'package:bushy/other/i_config.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EmailTool {
  static final EmailTool _instance = EmailTool._internal();
  factory EmailTool() => _instance;

  EmailTool._internal();

  final Dio _dio = Dio(
    BaseOptions(
      contentType: 'application/json',
      connectTimeout: const Duration(seconds: 160),
      receiveTimeout: const Duration(seconds: 160),
      sendTimeout: const Duration(seconds: 160),
    ),
  );

  bool get isLogin => _encodedToken != null && _encodedToken!.isNotEmpty;

  String? _account;
  String? _password;
  String? _gmailId;
  String? _token;
  int _expireTime = 0;
  String? _email;
  String? _encodedToken;
  String? _userId;

  final int _maxRetries = 1;

  bool get _isTokenValid {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return _token != null && _expireTime > now;
  }

  bool get _isEncodedTokenValid => _encodedToken != null && _userId != null;

  Map<String, String> langMap = {
    'en': 'en',
    'ar': 'ar-SA',
    'fr': 'fr-FR',
    'de': 'de-DE',
    'es': 'es-ES',
    'pt': 'pt-BR',
    'ja': 'ja-JP',
    'ko': 'ko-KR',
    'it': 'it-IT',
    'tr': 'tr-TR',
    'vi': 'vi-VN',
    'id': 'id-ID',
    'th': 'th-TH',
    'zh': 'zh-TW',
  };

  void _log(String msg) {
    print('[EmailTool] $msg');
  }

  Future<bool> login() async {
    _log('Starting login process');
    if (isLogin) {
      return true;
    }
    await _loadFromPrefs();
    _log('Loaded preferences');
    if (_isTokenValid && _email != null && _isEncodedTokenValid) {
      _log('Using cached token and email');
      return true;
    }

    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      _log('Login attempt $attempt');
      final success = await _tryLoginOnce();
      if (success) {
        _log('Login successful');
        await _saveToPrefs();
        _log('Saved preferences');
        return true;
      }
      _log('Login attempt $attempt failed');
    }
    _log('All login attempts failed');
    return false;
  }

  Future<bool> _tryLoginOnce() async {
    _log('Trying login once');
    if (!_isTokenValid) {
      _log('Token invalid, getting account');
      await _getAccount();
      if (_account == null || _password == null) {
        _log('Account or password is null');
        return false;
      }
      _log('Authenticating');
      await _auth();
      if (!_isTokenValid) {
        _log('Authentication failed');
        return false;
      }
    }

    if (_gmailId == null) {
      _log('Getting Gmail ID');
      await _getGmailId();
    }
    if (_gmailId == null) {
      _log('Gmail ID is null');
      return false;
    }

    if (_email == null) {
      _log('Generating email');
      await _generate();
    }
    if (_email == null) {
      _log('Email generation failed');
      return false;
    }

    _log('Sending verification code');
    await _sendVerificationCode(_email!);
    await Future.delayed(const Duration(seconds: 5));

    _log('Getting messages ID');
    final messagesId = await _getMessagesId(_email!);
    if (messagesId == null || messagesId.isEmpty) {
      _log('Messages ID is null or empty');
      return false;
    }

    _log('Getting verification code from message');
    final code = await _message(messagesId);
    if (code == null) {
      _log('Verification code is null');
      return false;
    }

    _log('Logging in with email and code');
    return await _emailLogin(_email!, code);
  }

  Future<void> _getAccount() async {
    _log('Getting account information');
    if (_account != null && _password != null) return;
    try {
      final res = await _dio.get(
        'https://www.cyberacter1.com//system/dict-item-config/e0bea688fe',
        queryParameters: {'dictCode': 'gmail', 'itemCode': 'account'},
      );
      if (res.statusCode == 200) {
        _account = res.data['val'];
        _password = res.data['remark'];
        _log('Account information retrieved: $_account  $_password');
      }
    } catch (e) {
      _log('Failed to get account: $e');
    }
  }

  Future<void> _getGmailId() async {
    _log('Getting Gmail ID');
    if (_gmailId != null) return;
    try {
      final res = await _dio.get(
        'https://www.cyberacter1.com//system/dict-item-config/e0bea688fe',
        queryParameters: {'dictCode': 'gmail', 'itemCode': 'gmailId'},
      );
      if (res.statusCode == 200) {
        _gmailId = res.data['val'];
        _log('Gmail ID retrieved: $_gmailId');
      }
    } catch (e) {
      _log('Failed to get gmailId: $e');
    }
  }

  Future<void> _auth() async {
    _log('Authenticating');
    try {
      final res = await _dio.post('https://22.do/api/v2/auth', data: {"mail": _account, "password": _password});
      if (res.statusCode == 200) {
        final data = res.data['data'];
        _token = data['Bearer'];
        _expireTime = data['expireTime'];
        _log('Authentication successful: $_token  $_expireTime');
      }
    } catch (e) {
      _log('Auth failed: $e');
    }
  }

  Future<void> _generate() async {
    _log('Generating email');
    try {
      final res = await _dio.post(
        'https://22.do/api/v2/gmail/generate',
        data: {"gmailId": _gmailId, "number": 1, "plus": true},
        options: Options(headers: {'Authorization': 'Bearer $_token'}),
      );
      if (res.statusCode == 200) {
        final items = res.data['data']['items'];
        final domains = res.data['data']['domains'];
        _email = '${items[0]}${domains[0]}';
        _log('Email generated: $_email');
      }
    } catch (e) {
      _log('Generate email failed: $e');
    }
  }

  Future<void> _sendVerificationCode(String email) async {
    _log('Sending verification code');
    try {
      await _dio.post('https://mobile-backend.flowgpt.com/auth/verify-email-code', data: {'email': email});
      _log('Verification code sent');
    } catch (e) {
      _log('Send verification code failed: $e');
    }
  }

  Future<String?> _getMessagesId(String email) async {
    _log('Getting messages ID');
    for (int attempt = 1; attempt <= 5; attempt++) {
      try {
        final res = await _dio.post(
          'https://22.do/api/v2/gmail/inbox',
          data: {'email': email, 'time': 0},
          options: Options(headers: {'Authorization': 'Bearer $_token'}),
        );
        final list = res.data['data'] as List?;
        if (list != null && list.isNotEmpty) {
          for (int i = list.length - 1; i >= 0; i--) {
            final id = list[i]['messageId'];
            if (id != null) {
              _log('Message ID retrieved');
              return id;
            }
          }
        }
      } catch (_) {}
      await Future.delayed(Duration(seconds: attempt));
    }
    _log('Failed to get messages ID');
    return null;
  }

  Future<String?> _message(String messageId) async {
    _log('Getting message content messageId = $messageId');
    try {
      final res = await _dio.post(
        'https://22.do/api/v2/gmail/message',
        data: {'messageId': messageId},
        options: Options(headers: {'Authorization': 'Bearer $_token'}),
      );
      final html = res.data['data']['html'];
      String verificationCodePattern = IConfig().emailCodeRegex ?? r'Verification Code[\s\S]*?>\s*(\d{4})\s*<';
      final regex = RegExp(verificationCodePattern);
      final match = regex.firstMatch(html);
      if (match != null) {
        final code = match.group(1);
        if (code != null) {
          _log('Verification code retrieved from message: $code');
          return code;
        }
      }
    } catch (_) {}
    _log('Failed to get message content');
    return null;
  }

  Future<bool> _emailLogin(String email, String code) async {
    _log('Logging in with email');
    try {
      final res = await _dio.post(
        'https://mobile-backend.flowgpt.com/auth/email-login',
        data: {'email': email, 'verificationCode': code},
      );
      if (res.statusCode == 200) {
        _encodedToken = res.data['encodedToken'];
        _userId = res.data['userId'];
        _log('Email login successful: $_encodedToken $_userId');
        return true;
      }
    } catch (e) {
      _log('Email login failed: $e');
    }
    return false;
  }

  Future<String?> translate(String text, {String? to = 'en', int retry = 0}) async {
    const maxRetry = 3;
    _log('Starting translation (attempt ${retry + 1})');

    try {
      // 根据 to去langMap找对应的他们 api要的语言
      final toLang = langMap[to];
      if (toLang == null) {
        _log('Language not supported: $to');
        return null;
      }

      if (!isLogin) {
        _log('Not logged in, attempting login');
        if (!await login()) {
          _log('Login failed during translation');
          return null;
        }
      }

      const url = 'https://mobile-backend.flowgpt.com/translate';
      final data = {'text': text, 'to': toLang};
      _log('Translation begin toLang = $toLang: $url, $data');

      final res = await _dio.post(
        url,
        data: data,
        options: Options(headers: {'authorization': 'Bearer $_encodedToken'}),
      );

      _log('Translation end: $res');

      if (res.statusCode == 200) {
        final result = res.data['text'];
        final success = res.data['success'];
        _log('Translation successful: $success - $result');

        if (success) {
          final isLanguage = await LanguageChecker().isLanguage(result, to ?? 'en');
          if (isLanguage) {
            return result;
          }
        }

        // 结果无效但未出错，重试
        if (retry < maxRetry - 1) {
          _log('Translation result invalid, retrying...');
          return await translate(text, to: to, retry: retry + 1);
        }
      }
    } catch (e) {
      _log('Translation failed: $e');

      // 异常时也尝试重试
      if (retry < maxRetry - 1) {
        _log('Translation exception, retrying...');
        return await translate(text, to: to, retry: retry + 1);
      }
    }

    _log('Translation ultimately failed after $maxRetry attempts');
    return null;
  }

  void logout() {
    _log('Logging out');
    _encodedToken = null;
    _userId = null;
    _saveToPrefs(); // 清理持久化
    _log('Logout complete');
  }

  /// 🧠 加载持久化数据
  Future<void> _loadFromPrefs() async {
    _log('Loading preferences');
    final prefs = await SharedPreferences.getInstance();
    _token = prefs.getString('et_token');
    _expireTime = prefs.getInt('et_expire') ?? 0;
    _email = prefs.getString('et_email');
    _encodedToken = prefs.getString('et_encodedToken');
    _userId = prefs.getString('et_userId');
    _log('Preferences loaded');
  }

  /// 💾 存储持久化数据
  Future<void> _saveToPrefs() async {
    _log('Saving preferences');
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('et_token', _token ?? '');
    await prefs.setInt('et_expire', _expireTime);
    await prefs.setString('et_email', _email ?? '');
    await prefs.setString('et_encodedToken', _encodedToken ?? '');
    await prefs.setString('et_userId', _userId ?? '');
    _log('Preferences saved');
  }
}
