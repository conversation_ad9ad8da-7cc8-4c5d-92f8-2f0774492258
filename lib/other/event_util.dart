import 'package:bushy/service/event_api.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

import 'log_util.dart';

Future<void> logEvent(
  String name, {
  Map<String, Object>? parameters,
  List<String>? strategies,
}) async {
  log.d('logEvent: $name, parameters: $parameters');
  try {
    FirebaseAnalytics.instance.logEvent(
      name: name,
      parameters: parameters,
    );

    EventApi().logCustomEvent(name: name, params: parameters);
  } catch (e) {
    log.e('logEvent error: $e');
  }
}
