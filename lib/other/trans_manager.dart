import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../common/i_dialog.dart';
import '../generated/locales.g.dart';
import 'i_cache.dart';
import 'i_enum.dart';
import 'i_router.dart';

class TransManager {
  static final TransManager _instance = TransManager._internal();

  factory TransManager() => _instance;

  TransManager._internal();

  int _clickCount = 0; // 点击次数
  DateTime? _firstClickTime; // 第一次点击的时间

  bool shouldShowDialog() {
    final now = DateTime.now();

    if (_firstClickTime == null || now.difference(_firstClickTime!).inMinutes > 1) {
      // 超过1分钟，重置计数器
      _firstClickTime = now;
      _clickCount = 1;
      return false;
    }

    _clickCount += 1;

    if (_clickCount >= 3) {
      _clickCount = 0; // 重置计数
      return true;
    }

    return false;
  }

  Future<void> handleTranslationClick() async {
    final hasShownDialog = ICache().std;

    if (TransManager().shouldShowDialog() && !hasShownDialog) {
      // 弹出提示弹窗
      showTranslationDialog();

      // 记录弹窗已显示
      ICache().std = true;
    }
  }

  void showTranslationDialog() {
    IDialog.alert(
      message: LocaleKeys.aoto_translation_msg.tr,
      confirmText: LocaleKeys.confirm.tr,
      onConfirm: () {
        SmartDialog.dismiss();
        showSubscriptionPage();
      },
    );
  }

  void showSubscriptionPage() {
    // 跳转订阅页
    IRouter.pushVip(VipSource.trans);
  }
}
