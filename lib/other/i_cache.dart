import 'dart:io';

import 'package:android_id/android_id.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_storage/get_storage.dart';
import 'package:uuid/v4.dart';

import '../data/user.dart';
import 'i_config.dart';

class ICache {
  static final ICache _instance = ICache._internal();

  ICache._internal();

  factory ICache() {
    return _instance;
  }

  final _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(),
  );

  final _box = GetStorage();

  ///获取设备ID
  static const String keyDeviceId = 'a1b2c3d4e5f6';

  Future<String> gDID({bool o = false}) async {
    var devicesId = await _storage.read(key: keyDeviceId);
    if (devicesId == null || devicesId.isEmpty) {
      devicesId = await _gDIDAsync();
      await _storage.write(key: keyDeviceId, value: devicesId);
    }

    return o ? devicesId : '${IConfig().platform}.$devicesId';
  }

  ///获取手机端用户设备标识
  Future<String> _gDIDAsync() async {
    String getCustomDeviceId() {
      String? deviceNo = const UuidV4().generate();
      return deviceNo;
    }

    if (Platform.isAndroid) {
      const androidIdPlugin = AndroidId();
      final String? androidId = await androidIdPlugin.getId();
      return androidId ?? getCustomDeviceId();
    } else if (Platform.isIOS) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor?.isNotEmpty == true ? iosInfo.identifierForVendor! : getCustomDeviceId();
    } else {
      return getCustomDeviceId();
    }
  }

  /// clk
  bool get isBusy => _box.read<bool>('x7y8z9a1b2') ?? false;
  set isBusy(bool value) => _box.write('x7y8z9a1b2', value);

  /// start restart app
  bool get isRA => _box.read<bool>('c3d4e5f6g7') ?? false;
  set isRA(bool value) => _box.write('c3d4e5f6g7', value);

  /// chatBgImage
  String get cbi => _box.read<String>('h8i9j0k1l2') ?? '';
  set cbi(String value) => _box.write('h8i9j0k1l2', value);

  // user
  User? get user {
    final map = _box.read('m3n4o5p6q7');
    final user = map == null ? null : User.fromJson(map!);
    return user;
  }

  set user(User? value) {
    if (value == null) {
      return;
    }
    final map = value.toJson();
    _box.write('m3n4o5p6q7', map);
  }

  // send msg couont
  int get mc => _box.read<int>('r8s9t0u1v2') ?? 0;
  set mc(int value) => _box.write('r8s9t0u1v2', value);

  // tab create count
  int get tcc => _box.read<int>('w3x4y5z6a7') ?? 0;
  set tcc(int value) => _box.write('w3x4y5z6a7', value);

  // tab monents count
  int get tmc => _box.read<int>('b8c9d0e1f2') ?? 0;
  set tmc(int value) => _box.write('b8c9d0e1f2', value);

  // hasShownTranslationDialog
  bool get std => _box.read<bool>('g3h4i5j6k7') ?? false;
  set std(bool value) => _box.write('g3h4i5j6k7', value);

  // installTime
  int get itd => _box.read<int>('l8m9n0o1p2') ?? 0;
  set itd(int value) => _box.write('l8m9n0o1p2', value);

  // lastRewardDate
  int get lrd => _box.read<int>('q3r4s5t6u7') ?? 0;
  set lrd(int value) => _box.write('q3r4s5t6u7', value);

  // 会话 ID 列表， 用于处理是否播放问候语
  final String _sessionKey = 'v8w9x0y1z2';

  // 获取所有存储的会话 ID（作为 List 类型）
  List<int> _getSessionIds() {
    List<dynamic> sessionIds = _box.read<List<dynamic>>(_sessionKey) ?? [];
    // 将 List<dynamic> 转换为 List<int>
    return sessionIds.map((e) => e as int).toList();
  }

  // 将会话 ID 存储到缓存中，确保排重
  void addSessionId(int sessionId) {
    List<int> sessionIds = _getSessionIds();

    // 只添加不重复的会话 ID
    if (!sessionIds.contains(sessionId)) {
      sessionIds.add(sessionId);
      _box.write(_sessionKey, sessionIds); // 更新缓存
    }
  }

  // 检查是否已经存储了该会话 ID
  bool isSessionExist(int sessionId) {
    List<int> sessionIds = _getSessionIds();
    return sessionIds.contains(sessionId);
  }

  // 移除指定的会话 ID
  void removeSessionId(int sessionId) {
    List<int> sessionIds = _getSessionIds();
    sessionIds.remove(sessionId);
    _box.write(_sessionKey, sessionIds); // 更新缓存
  }

  // block ids 列表，用于记录被block的id
  final String _blockKey = 'b1l2o3c4k5';

  // 获取所有被block的id（作为 List 类型）
  List<String?> _getBlockIds() {
    List<dynamic> blockIds = _box.read<List<dynamic>>(_blockKey) ?? [];
    return blockIds.map((e) => e as String?).toList();
  }

  // 添加block id到缓存中，确保排重
  void addBlockId(String? blockId) {
    List<String?> blockIds = _getBlockIds();
    if (!blockIds.contains(blockId)) {
      blockIds.add(blockId);
      _box.write(_blockKey, blockIds);
    }
  }

  // 检查id是否已被block
  bool isBlocked(String? blockId) {
    List<String?> blockIds = _getBlockIds();
    return blockIds.contains(blockId);
  }

  // 移除指定的block id
  void removeBlockId(String? blockId) {
    List<String?> blockIds = _getBlockIds();
    blockIds.remove(blockId);
    _box.write(_blockKey, blockIds);
  }

  // Locale
  final String _languageCode = '_languageCode';
  final String _countryCode = '_countryCode';
  Locale? get locale {
    final languageCode = _box.read<String>(_languageCode);
    final countryCode = _box.read<String>(_countryCode);
    if (languageCode != null) {
      return Locale(languageCode, countryCode);
    }
    return null;
  }

  set locale(Locale? value) {
    if (value == null) {
      return;
    }
    _box.write(_languageCode, value.languageCode);
    _box.write(_countryCode, value.countryCode);
  }
}
