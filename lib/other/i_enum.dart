enum VipSource {
  locktext,
  lockpic,
  lockvideo,
  lockaudio,
  send,
  homevip,
  mevip,
  chatvip,
  launch,
  relaunch,
  viprole,
  call,
  acceptcall,
  creimg,
  crevideo,
  undrphoto,
  postpic,
  postvideo,
  undrchar,
  videochat,
  trans,
  dailyrd,
}

enum ConsumeSource {
  home,
  chat,
  send,
  profile,
  text,
  audio,
  call,
  unlcokText,
  undr,
  creaimg,
  creavideo,
  album,
  gift_clo,
  gift_toy,
}

extension GlobFromExt on ConsumeSource {
  int get gems {
    switch (this) {
      case ConsumeSource.text:
        return 2;
      case ConsumeSource.audio:
        return 4;
      case ConsumeSource.call:
        return 10;
      case ConsumeSource.creaimg:
        return 8;
      case ConsumeSource.creavideo:
        return 10;
      case ConsumeSource.unlcokText:
        return 4;
      case ConsumeSource.undr:
        return 100;
      default:
        return 0;
    }
  }
}

enum MsgSource {
  text,
  sendText,
  video,
  audio,
  photo,
  waitAnswer,
  tips,
  scenario,
  intro,
  welcome,
  gift,
  clothe,
}

extension MsgSourceExtension on MsgSource {
  static const Map<MsgSource, String> _valueMap = {
    MsgSource.text: 'TEXT_GEN',
    MsgSource.video: 'VIDEO',
    MsgSource.audio: 'AUDIO',
    MsgSource.photo: 'PHOTO',
    MsgSource.waitAnswer: 'waitAnswer',
    MsgSource.tips: 'tips',
    MsgSource.scenario: 'scenario',
    MsgSource.intro: 'intro',
    MsgSource.welcome: 'welcome',
    MsgSource.sendText: 'sendText',
    MsgSource.gift: 'GIFT',
    MsgSource.clothe: 'CLOTHE',
  };

  static const Map<String, MsgSource> _sourceMap = {
    'TEXT_GEN': MsgSource.text,
    'VIDEO': MsgSource.video,
    'AUDIO': MsgSource.audio,
    'PHOTO': MsgSource.photo,
    'waitAnswer': MsgSource.waitAnswer,
    'tips': MsgSource.tips,
    'scenario': MsgSource.scenario,
    'intro': MsgSource.intro,
    'welcome': MsgSource.welcome,
    'sendText': MsgSource.sendText,
    'GIFT': MsgSource.gift,
    'CLOTHE': MsgSource.clothe,
  };

  String get value => _valueMap[this] ?? 'TEXT_GEN';

  static MsgSource? fromSource(String? source) => _sourceMap[source];
}

enum SendStatus {
  sending, // 0 - 发送中
  sent, // 1 - 发送成功
  failed // 2 - 发送失败
}

extension SendStatusExt on SendStatus {
  static const Map<SendStatus, int> _statusToInt = {
    SendStatus.sending: 0,
    SendStatus.sent: 1,
    SendStatus.failed: 2,
  };

  static const Map<int, SendStatus> _intToStatus = {
    0: SendStatus.sending,
    1: SendStatus.sent,
    2: SendStatus.failed,
  };

  int toInt() => _statusToInt[this]!;

  static SendStatus fromInt(int value) {
    return _intToStatus[value] ?? (throw ArgumentError('Invalid value for SendStatus: $value'));
  }
}

enum LockLevel {
  normal,
  private,
}

extension LockLevelExt on LockLevel {
  static const Map<LockLevel, String> _levelToStr = {
    LockLevel.normal: 'NORMAL',
    LockLevel.private: 'PRIVATE',
  };

  static const Map<String, LockLevel> _strToLevel = {
    'NORMAL': LockLevel.normal,
    'PRIVATE': LockLevel.private,
  };

  String get value => _levelToStr[this]!;

  static LockLevel fromValue(String value) => _strToLevel[value]!;
}

enum MsgEvent {
  add,
  update,
  remove,
}

enum SessionEvent {
  add,
  update,
  remove,
  clear,
}

enum CreateType {
  photo,
  video,
}

enum IAPEvent {
  vipSucc,
  goldSucc,
}

enum FollowEvent {
  follow,
  unfollow,
}

enum DialogTag {
  sigin,
  chatLevel,
  nickname,
  clotheLoading,
}

enum CallState {
  calling,
  incoming,
  listening,
  answering,
  answered,
}

enum ChaterListType {
  favored,
  video,
  dressup,
  realistic,
  anime,
}
