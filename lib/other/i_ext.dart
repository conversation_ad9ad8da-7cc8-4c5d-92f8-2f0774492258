import 'dart:convert';
import 'dart:math';

String formatNumber(double? value) {
  if (value == null) {
    return '0';
  }
  if (value % 1 == 0) {
    // 如果小数部分为 0，返回整数
    return value.toInt().toString();
  } else {
    // 如果有小数部分，返回原值
    return value.toString();
  }
}

String formatAudioTime(int seconds) {
  int hours = seconds ~/ 3600;
  int minutes = (seconds % 3600) ~/ 60;
  int secs = seconds % 60;

  if (hours > 0) {
    return '$hours:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}s';
  } else if (minutes > 0) {
    return '$minutes:${secs.toString().padLeft(2, '0')}s';
  } else {
    return '${secs}s';
  }
}

String numberPart(String skuId) {
  return skuId.replaceAll(RegExp(r'[^0-9]'), '');
}

String unitPart(String skuId) {
  return skuId.replaceAll(RegExp(r'[0-9]'), ''); // 保留非数字部分
}

String numFixed(dynamic nums, {int position = 2}) {
  double num = nums is double ? nums : double.parse(nums.toString());

  // 计算缩放因子，用来控制小数点后的精度
  double scale = pow(10, position).toDouble();

  // 使用 floor 来实现向下舍入，避免四舍五入
  double result = (num * scale).floorToDouble() / scale;

  // 转换为字符串
  String numString = result.toStringAsFixed(position);

  // 如果结果有 '.0'，则去掉它
  return numString.endsWith('.0') ? numString.substring(0, numString.lastIndexOf('.')) : numString;
}

String formatVideoDuration(int seconds) {
  // 计算小时、分钟、秒
  final hours = seconds ~/ 3600;
  final minutes = (seconds % 3600) ~/ 60;
  final secs = seconds % 60;

  // 格式化成视频时长格式
  if (hours > 0) {
    // 如果时长包含小时，则显示 HH:mm:ss
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  } else {
    // 如果时长不足一小时，则显示 mm:ss
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }
}

String formatTimestamp(int timestampInMilliseconds) {
  DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestampInMilliseconds);
  String year = dateTime.year.toString();
  String month = dateTime.month.toString().padLeft(2, '0'); // 保证两位数格式
  String day = dateTime.day.toString().padLeft(2, '0'); // 保证两位数格式

  return '$year-$month-$day';
}

var _random = Random();

extension ListKx<T> on List<T>? {
  T? get randomOrNull {
    if (this == null || this!.isEmpty) {
      return null;
    } else {
      return this![_random.nextInt(this!.length)];
    }
  }

  bool get isNullOrEmpty => this == null || this!.isEmpty;

  String? get toJsonString {
    if (this == null || this!.isEmpty) {
      return null;
    } else {
      try {
        return jsonEncode(this);
      } catch (e) {
        e.toString();
      }
      return null;
    }
  }
}

// extension HexColor on Color {
//   // 从 HEX 字符串生成颜色，支持各种常见格式：#RRGGBB, #AARRGGBB, 0xAARRGGBB, 0xRRGGBB, RRGGBB
//   static Color fromHex(String hexString) {
//     // 去掉字符串中的空格
//     hexString = hexString.trim().toUpperCase();

//     // 处理 0x 开头的格式
//     if (hexString.startsWith('0X')) {
//       // 处理 0x 格式（支持 6 位和 8 位）
//       return Color(int.parse(hexString));
//     }
//     // 处理 # 开头的格式
//     else if (hexString.startsWith('#')) {
//       if (hexString.length == 7) {
//         // #RRGGBB 格式（6 位），默认 alpha 为 255 (FF)
//         return Color(int.parse('0xFF${hexString.substring(1)}'));
//       } else if (hexString.length == 9) {
//         // #AARRGGBB 格式（8 位）
//         return Color(int.parse('0x${hexString.substring(1)}'));
//       } else {
//         throw const FormatException('Invalid HEX color format');
//       }
//     }
//     // 处理没有任何前缀的 6 位格式
//     else if (hexString.length == 6) {
//       return Color(int.parse('0xFF$hexString'));
//     }
//     // 处理 8 位格式：AARRGGBB
//     else if (hexString.length == 8) {
//       return Color(int.parse('0x$hexString'));
//     } else {
//       throw const FormatException('Invalid HEX color format');
//     }
//   }

//   // 将 Color 转换为 HEX 字符串
//   String toHex() {
//     return '#${value.toRadixString(16).padLeft(8, '0').toUpperCase()}';
//   }
// }
