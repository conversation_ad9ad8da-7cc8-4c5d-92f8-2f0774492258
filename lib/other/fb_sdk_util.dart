import 'dart:io';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/services.dart';

class FBSDKUtil {
  static const MethodChannel _channel = MethodChannel('facebook_sdk_channel');

  // 记录Facebook SDK是否已经初始化
  static bool _isInitialized = false;
  static String? _cachedAppId;
  static String? _cachedClientToken;

  /// 获取初始化状态
  static bool get isInitialized => _isInitialized;

  /// 从远程配置获取Facebook SDK配置
  static Future<Map<String, String>?> _getConfigFromRemote() async {
    try {
      final String facebookAppIdKey = Platform.isAndroid ? 'facebook_app_id_android' : 'facebook_app_id_ios';
      final String facebookClientTokenKey =
          Platform.isAndroid ? 'facebook_client_token_android' : 'facebook_client_token_ios';

      final remoteConfig = FirebaseRemoteConfig.instance;

      final String facebookAppId = remoteConfig.getString(facebookAppIdKey);
      final String facebookClientToken = remoteConfig.getString(facebookClientTokenKey);

      // 验证配置是否有效
      if (facebookAppId.isEmpty || facebookClientToken.isEmpty) {
        print('Facebook SDK配置无效，跳过初始化');
        return null;
      }

      return {
        'appId': facebookAppId,
        'clientToken': facebookClientToken,
      };
    } catch (e) {
      print('获取Facebook SDK配置失败: $e');
      return null;
    }
  }

  /// 初始化Facebook SDK（自动获取配置）
  static Future<void> initializeWithRemoteConfig() async {
    final config = await _getConfigFromRemote();
    if (config != null) {
      print('获取到Facebook SDK配置 开始初始化');
      await initializeFacebookSDK(
        appId: config['appId']!,
        clientToken: config['clientToken']!,
      );
    } else {
      print('未获取到有效的Facebook SDK配置，跳过初始化');
      throw Exception('Facebook SDK配置无效');
    }
  }

  /// 检查Facebook SDK是否已初始化（原生端状态）
  static Future<bool> checkNativeInitializationStatus() async {
    try {
      final result = await _channel.invokeMethod('isFacebookSDKInitialized');
      return result as bool? ?? false;
    } catch (e) {
      print('检查Facebook SDK初始化状态失败: $e');
      return false;
    }
  }

  /// 初始化Facebook SDK
  /// [appId] Facebook应用ID
  /// [clientToken] Facebook客户端令牌
  static Future<void> initializeFacebookSDK({
    required String appId,
    required String clientToken,
  }) async {
    try {
      // 缓存配置信息
      _cachedAppId = appId;
      _cachedClientToken = clientToken;

      // 检查参数有效性
      if (appId.isEmpty || clientToken.isEmpty) {
        throw PlatformException(
          code: 'INVALID_ARGUMENTS',
          message: 'App ID and Client Token cannot be empty',
        );
      }

      final result = await _channel.invokeMethod('initializeFacebookSDK', {
        'appId': appId,
        'clientToken': clientToken,
      });

      // 等待一段时间，确保原生端有足够的时间完成异步初始化
      await Future.delayed(const Duration(seconds: 1));

      // 验证初始化状态
      final nativeInitialized = await checkNativeInitializationStatus();
      _isInitialized = nativeInitialized;

      if (_isInitialized) {
        print('Facebook SDK初始化成功: $result');
      } else {
        print('Facebook SDK初始化可能不完整，原生端报告未初始化');
      }
    } on PlatformException catch (e) {
      print('Facebook SDK初始化失败: ${e.message}');
      _isInitialized = false;
      rethrow;
    }
  }

  /// 网络恢复后重新初始化（如果之前未成功初始化）
  static Future<void> retryInitializationIfNeeded() async {
    try {
      if (!_isInitialized) {
        print('网络恢复，尝试重新初始化Facebook SDK');

        // 如果有缓存配置，直接使用
        if (_cachedAppId != null && _cachedClientToken != null) {
          await initializeFacebookSDK(
            appId: _cachedAppId!,
            clientToken: _cachedClientToken!,
          );
        } else {
          // 如果没有缓存配置，重新从远程获取
          await initializeWithRemoteConfig();
        }
      }
    } catch (e) {
      print('Facebook SDK 初始化重试失败: $e');
    }
  }
}
