import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../generated/locales.g.dart';
import 'i_theme.dart';

void report() {
  void request() async {
    SmartDialog.showLoading();
    await Future.delayed(const Duration(seconds: 1));
    SmartDialog.dismiss();
    SmartDialog.showNotify(msg: LocaleKeys.report_successful.tr, notifyType: NotifyType.success);
    SmartDialog.dismiss(status: SmartStatus.allCustom);
  }

  var actions = <String, VoidCallback>{
    LocaleKeys.spam.tr: request,
    LocaleKeys.violence.tr: request,
    LocaleKeys.child_abuse.tr: request,
    LocaleKeys.copyright.tr: request,
    LocaleKeys.personal_details.tr: request,
    LocaleKeys.illegal_drugs.tr: request,
  };

  SmartDialog.show(
    alignment: Alignment.bottomCenter,
    builder: (context) => Container(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom + 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.horizontal(left: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: actions.entries.map((entry) {
          return Column(
            children: [
              SizedBox(
                height: 52,
                child: ListTile(
                  title: Text(
                    entry.key,
                    textAlign: TextAlign.center,
                    style: ITStyle.l3reg(Colors.black),
                  ),
                  onTap: entry.value,
                ),
              ),
              Container(
                height: 1,
                color: const Color(0xffF2F2F2),
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
            ],
          );
        }).toList(),
      ),
    ),
  );
}
