import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ITheme {
  ITheme._();
}

class IColor {
  IColor._();

  static const Color brand = Color(0xff906BF7);
  static const Color brand1 = Color(0x1A906BF7); //brand 10%透明度
  static const Color brand2 = Color(0x33906BF7); //brand 20%透明度
  static const Color brand5 = Color(0x80906BF7); //brand 50%透明度
  static const Color brand8 = Color(0xCC906BF7); //brand 80%透明度

  static const List<Color> brandjb = [
    Color(0xffCED4FC),
    Color(0xffDFCFFB),
    Color(0xffECCDFF),
  ];

  static const Color n01 = Color(0xff222222);
  static const Color n02 = Color(0xff1C1C1C);
  static const Color n03 = Color(0xff727374);
  static const Color n04 = Color(0xFFA8A8A8);
  static const Color n05 = Color(0xFFC9C9C9);
  static const Color n06 = Color(0xFFDEDEDE);
  static const Color n07 = Color(0xFFEBEBEB);
  static const Color n08 = Color(0xFFF2F2F2);
  static const Color n09 = Color(0xffF7F7F7);
  static const Color n10 = Color(0xFFFFFFFF);

  static const Color red = Color(0xffE44341);
  static const Color green = Color(0xff30D158);
  static const Color orther01 = Color(0xffFF6DB3);
  static const Color orther03 = Color(0xffF31D1D);

  // 白色
  static const Color white1 = Color(0x1AFFFFFF); // 10%透明度
  static const Color white2 = Color(0x33FFFFFF); // 20%透明度
  static const Color white5 = Color(0x80FFFFFF); // 50%透明度
  static const Color white8 = Color(0xCCFFFFFF); // 80%透明度

  // 黑色
  static const Color black = Color(0xFF1C1C1C);
  static const Color black1 = Color(0x1A1C1C1C); // 10%透明度
  static const Color black2 = Color(0x331C1C1C); // 20%透明度
  static const Color black5 = Color(0x801C1C1C); // 50%透明度
  static const Color black8 = Color(0xCC1C1C1C); // 80%透明度
}

class ITStyle {
  ITStyle._();

  static TextStyle textStyle(double fontSize, FontWeight fontWeight, Color? color) {
    return GoogleFonts.montserrat(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? IColor.n10,
    );
  }

  static TextStyle l1sem(Color? color) => textStyle(32, FontWeight.w700, color);
  static TextStyle l1reg(Color? color) => textStyle(32, FontWeight.w400, color);
  static TextStyle l2sem(Color? color) => textStyle(24, FontWeight.w700, color);
  static TextStyle l2reg(Color? color) => textStyle(24, FontWeight.w400, color);
  static TextStyle l21sem(Color? color) => textStyle(20, FontWeight.w700, color);
  static TextStyle l21reg(Color? color) => textStyle(20, FontWeight.w400, color);
  static TextStyle l3sem(Color? color) => textStyle(16, FontWeight.w700, color);
  static TextStyle l3reg(Color? color) => textStyle(16, FontWeight.w400, color);
  static TextStyle l4sem(Color? color) => textStyle(14, FontWeight.w700, color);
  static TextStyle l4reg(Color? color) => textStyle(14, FontWeight.w400, color);
  static TextStyle l5sem(Color? color) => textStyle(12, FontWeight.w700, color);
  static TextStyle l5reg(Color? color) => textStyle(12, FontWeight.w400, color);
  static TextStyle l6sem(Color? color) => textStyle(10, FontWeight.w700, color);
  static TextStyle l6reg(Color? color) => textStyle(10, FontWeight.w400, color);
  static TextStyle l7reg(Color? color) => textStyle(8, FontWeight.w400, color);
}
