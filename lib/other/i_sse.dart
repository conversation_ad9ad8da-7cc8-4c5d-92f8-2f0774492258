import 'dart:async';

import 'package:bushy/common/language_checker.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_config.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/service/i_p.dart';
import 'package:flutter_client_sse/constants/sse_request_type_enum.dart';
import 'package:flutter_client_sse/flutter_client_sse.dart';

class ISSE {
  static final ISSE _instance = ISSE._internal();
  late String url;
  late StreamController<String> _controller;
  StreamSubscription? _subscription;
  bool _isClosed = false; // 新增一个标志来防止多次关闭

  factory ISSE() {
    _instance.url = IConfig().baseUrl;
    return _instance;
  }

  ISSE._internal() {
    _controller = StreamController<String>.broadcast();
  }

  // 获取 SSE 事件流
  Stream<String> get stream => _controller.stream;

  // 关闭连接
  void close() {
    if (!_isClosed) {
      _subscription?.cancel();
      _controller.close(); // 确保只关闭一次流
      _isClosed = true; // 设置标志，防止多次关闭
    }
  }

  Future<void> sendMessage(String charId, int conversationId, String uid, String message) async {
    final requestUrl = '$url/v2/message/conversation/ask/v1/i18n';

    String? translateQuestion;
    final isLanguage = await LanguageChecker().isLanguage(message, 'en');
    if (!isLanguage) {
      translateQuestion = await IP.translateText(message, 'en');
    }

    final body = {
      'character_id': charId,
      'conversation_id': conversationId,
      'message': message,
      'user_id': uid,
      'translate_text': translateQuestion
    };

    try {
      // 如果已有订阅，先取消
      await _subscription?.cancel(); // 安全取消，如果没有订阅则不会做任何事
      final deviceId = await ICache().gDID();

      // 重新建立新的订阅
      _subscription = SSEClient.subscribeToSSE(
        method: SSERequestType.POST,
        url: requestUrl,
        header: {
          "Accept": "application/json",
          "Content-Type": "application/json",
          'device-id': deviceId,
          'platform': IConfig().platform,
        },
        body: body,
      ).listen(
        (event) {
          if (!_isClosed) {
            // 确保流未关闭时才添加数据
            if (event.event == 'error') {
              log.e('Error receiving SSE event: ${event.data}');
              _controller.add(event.data ?? 'error: network error');
              close(); // 关闭连接
            } else {
              if (event.data!.isNotEmpty) {
                _controller.add(event.data!);
              }
            }
          }
        },
        onError: (e) {
          // 捕获并处理 SSE 订阅的错误
          log.e('onError: Error receiving SSE event: $e');
          if (!_isClosed) {
            // 确保流未关闭时才添加错误
            _controller.addError(e); // 将错误添加到流中，方便调用者处理
          }
        },
      );
    } catch (e) {
      log.e('Error sending message: $e');
      SSEClient.unsubscribeFromSSE();
      if (!_isClosed) {
        // 确保流未关闭时才添加错误
        _controller.addError(e); // 将错误添加到流中，方便调用者处理
      }
    }
  }
}
