import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get_navigation/src/router_report.dart';

import 'user_helper.dart';

class _NOBS extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    RouterReportManager.reportCurrentRoute(route);
    NavObs().curRoute = route;
    NavObs().push(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) async {
    RouterReportManager.reportRouteDispose(route);
    NavObs().curRoute = previousRoute;
    NavObs().pop(route, previousRoute);
  }
}

class NavObs {
  static NavObs? _instance;

  factory NavObs() {
    return _instance ??= NavObs._internal();
  }

  NavObs._internal();

  final _NOBS observer = _NOBS();

  /// 自定义的 RouteObserver，方便页面订阅
  final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

  /// 路由栈队列
  final Queue<Route<dynamic>> routeQueue = DoubleLinkedQueue<Route<dynamic>>();

  /// 当前路由
  Route<dynamic>? curRoute;

  /// 添加路由到队列
  void push(Route<dynamic> route, Route<dynamic>? previousRoute) {
    routeQueue.add(route);
  }

  /// 从队列中移除路由
  void pop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    routeQueue.remove(route);

    if (previousRoute?.settings.name == '/') {
      UserHelper().getUserInfo();
    }
  }

  /// 检查队列中是否包含某个页面
  bool containPage(String name) {
    for (var route in routeQueue) {
      if (route.settings.name == name) {
        return true;
      }
    }
    return false;
  }
}
