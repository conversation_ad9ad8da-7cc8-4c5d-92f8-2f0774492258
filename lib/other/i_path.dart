import 'package:get/get_navigation/src/routes/get_route.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';

import '../common/image_preview_page.dart';
import '../common/video_preview_page.dart';
import '../pages/chat/chat_page.dart';
import '../pages/chat/msg_v.dart';
import '../pages/genc/gen_res.dart';
import '../pages/home/<USER>';
import '../pages/home/<USER>';
import '../pages/home/<USER>';
import '../pages/iap/coin_v.dart';
import '../pages/iap/vip_v.dart';
import '../pages/me/me_v.dart';
import '../pages/mian/main_v.dart';
import '../pages/mian/splash_v.dart';
import '../pages/phone/p_g_v.dart';
import '../pages/phone/phone_v.dart';

class IPath {
  IPath._();

  static const String root = '/';
  static const String splash = '/splash';
  static const String search = '/search';
  static const String vip = '/vip';
  static const String imagePreview = '/imagePreview';
  static const String videoPreview = '/videoPreview';
  static const String msg = '/msg';
  static const String profile = '/profile';
  static const String gems = '/gems';
  static const String phone = '/phone';
  static const String phoneGuide = '/phoneGuide';
  static const String genPage = '/genPage';
  static const String chatList = '/chatList';
  static const String momentList = '/momentList';
  static const String setting = '/setting';

  static final List<GetPage> pages = [
    GetPage(name: root, page: () => const MainV()),
    GetPage(name: splash, page: () => const SplashV()),
    GetPage(name: search, page: () => const SearchV()),
    GetPage(
      name: imagePreview,
      page: () => const ImagePreviewPage(),
      transition: Transition.zoom,
      fullscreenDialog: true,
    ),
    GetPage(name: videoPreview, page: () => const VideoPreviewPage(), fullscreenDialog: true),
    GetPage(name: msg, page: () => const MsgV()),
    GetPage(name: vip, page: () => const VipV()),
    GetPage(name: profile, page: () => ChaterDetail()),
    GetPage(name: gems, page: () => const CoinV()),
    GetPage(name: phone, page: () => PhoneV()),
    GetPage(name: phoneGuide, page: () => const PGV()),
    GetPage(name: genPage, page: () => GenRes()),
    GetPage(name: chatList, page: () => const ChatPage()),
    GetPage(name: momentList, page: () => MomentsV()),
    GetPage(name: setting, page: () => const MeV()),
  ];
}
