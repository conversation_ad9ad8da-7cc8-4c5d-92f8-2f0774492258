import 'package:bushy/common/i_dialog.dart';
import 'package:bushy/data/chater.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/event_util.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_path.dart';
import 'package:bushy/other/info_helper.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:bushy/service/i_p.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:url_launcher/url_launcher.dart';

class IRouter {
  IRouter._();

  static void pushMsgCall({
    required int sessionId,
    required Chater role,
  }) {
    logEvent('c_call');
    if (!UserHelper().isVip.value) {
      IRouter.pushVip(VipSource.call);
      return;
    }

    if (!UserHelper().isBalanceEnough(ConsumeSource.call)) {
      IRouter.pushGem(ConsumeSource.call);
      return;
    }
    if (sessionId <= 0) {
      SmartDialog.showToast(LocaleKeys.some_error_try_again.tr);
      log.e('sessionId <= 0');
      return;
    }

    IRouter.pushPhone(sessionId: sessionId, role: role, showVideo: false);
  }

  static void pushSetting() {
    Get.toNamed(IPath.setting);
  }

  static void pushRoot() {
    Get.offAllNamed(IPath.root);
  }

  static void pushSearch() async {
    Get.toNamed(IPath.search);
  }

  static void pushChatList() {
    Get.toNamed(IPath.chatList);
  }

  static void pushMomentList() {
    Get.toNamed(IPath.momentList);
  }

  static Future<void> pushChat(String? roleId, {bool showLoading = true}) async {
    if (roleId == null) {
      _dismissAndShowErrorToast('roleId is null');
      return;
    }

    try {
      if (showLoading) {
        SmartDialog.showLoading();
      }

      // 使用 Future.wait 来同时执行查角色和查会话
      var results = await Future.wait([
        IP.loadRoleById(roleId), // 查角色
        IP.addSession(roleId), // 查会话
      ]);

      var role = results[0];
      var session = results[1];

      // 检查角色和会话是否为 null
      if (role == null) {
        _dismissAndShowErrorToast('role is null');
        return;
      }
      if (session == null) {
        _dismissAndShowErrorToast('session is null');
        return;
      }

      SmartDialog.dismiss();
      Get.toNamed(IPath.msg, arguments: {'role': role, 'session': session});
    } catch (e) {
      SmartDialog.dismiss(); // 确保发生异常时关闭加载提示
      SmartDialog.showNotify(msg: e.toString(), notifyType: NotifyType.error);
    }
  }

  static void _dismissAndShowErrorToast(String message) {
    SmartDialog.dismiss();
    SmartDialog.showToast(message);
  }

  static void pushVip(VipSource from) {
    Get.toNamed(IPath.vip, arguments: from);
  }

  static void pushProfile(Chater role) {
    Get.toNamed(IPath.profile, arguments: role);
  }

  static void pushGem(ConsumeSource from) {
    Get.toNamed(IPath.gems, arguments: from);
  }

  static void offGem(ConsumeSource from) {
    Get.offNamed(IPath.gems, arguments: from);
  }

  static Future<T?>? pushPhone<T>({
    required int sessionId,
    required Chater role,
    required bool showVideo,
    CallState callState = CallState.calling,
  }) async {
    // 检查 Mic 权限 和 语音权限
    if (!await checkPermissions()) {
      showNoPermissionDialog();
      return null;
    }

    return Get.toNamed(
      IPath.phone,
      arguments: {
        'sessionId': sessionId,
        'role': role,
        'callState': callState,
        'showVideo': showVideo,
      },
    );
  }

  static Future<T?>? offPhone<T>({
    required Chater role,
    required bool showVideo,
    CallState callState = CallState.calling,
  }) async {
    // 检查 Mic 权限 和 语音权限
    if (!await checkPermissions()) {
      showNoPermissionDialog();
      return null;
    }
    var seesion = await IP.addSession(role.id ?? ''); // 查会话
    final sessionId = seesion?.id;
    if (sessionId == null) {
      _dismissAndShowErrorToast('sessionId is null');
      return null;
    }

    return Get.offNamed(
      IPath.phone,
      arguments: {
        'sessionId': sessionId,
        'role': role,
        'callState': callState,
        'showVideo': showVideo,
      },
    );
  }

  /// 检查麦克风和语音识别权限，返回是否已授予所有权限
  static Future<bool> checkPermissions() async {
    // 初始化 SpeechToText 以检查语音识别权限
    SpeechToText speechToText = SpeechToText();
    bool available = await speechToText.initialize();

    log.d('语音识别是否可用: $available');

    // 如果语音识别未初始化成功，返回 false
    if (!available) {
      return false;
    }

    // 如果麦克风和语音识别权限均已授予，返回 true
    return true;
  }

  // 没有权限提示
  static Future<void> showNoPermissionDialog() async {
    IDialog.alert(
      message: LocaleKeys.microphone_permission_required.tr,
      onConfirm: () async {
        await openAppSettings();
      },
      cancelText: LocaleKeys.cancel.tr,
      confirmText: LocaleKeys.open_settings.tr,
    );
  }

  static Future<T?>? pushPhoneGuide<T>({
    required Chater role,
  }) {
    return Get.toNamed(
      IPath.phoneGuide,
      arguments: {'role': role},
    );
  }

  static Future<T?>? pushCreate<T>({
    required Chater role,
    required CreateType type,
  }) {
    return Get.toNamed(
      IPath.genPage,
      arguments: {
        'role': role,
        'type': type,
      },
    );
  }

  static void pushImagePreview(String imageUrl) {
    Get.toNamed(IPath.imagePreview, arguments: imageUrl);
  }

  static void pushVideoPreview(String url) {
    Get.toNamed(IPath.videoPreview, arguments: url);
  }

  static void toEmail() async {
    final version = await InfoHeper().version();
    final device = await ICache().gDID();
    final uid = UserHelper().user?.id;

    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: 'mailto:<EMAIL>', // 收件人
      query:
          "subject=Feedback&body=version: $version\ndevice: $device\nuid: $uid\nPlease input your problem:\n", // 设置默认主题和正文内容
    );

    launchUrl(emailUri);
  }

  static void toPrivacy() {
    launchUrl(Uri.parse('https://bushyai.com/Privacy/index.html'));
  }

  static void toTerms() {
    launchUrl(Uri.parse('https://bushyai.com/Terms/index.html'));
  }
}
