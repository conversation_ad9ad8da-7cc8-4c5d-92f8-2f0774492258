import 'dart:async';
import 'dart:io';

import 'package:bushy/data/product_model.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';

import '../common/i_dialog.dart';
import '../data/order_and.dart';
import '../data/order_ios.dart';
import '../generated/locales.g.dart';
import '../service/i_p.dart';
import 'event_util.dart';
import 'i_enum.dart';
import 'i_ext.dart';
import 'log_util.dart';
import 'user_helper.dart';

class IAPUtil {
  // 单例模式
  static final IAPUtil _instance = IAPUtil._internal();
  factory IAPUtil() => _instance;
  IAPUtil._internal() {
    _initIAP();
  }

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  Set<String> _consumableIds = {};
  Set<String> _subscriptionIds = {};

  List<ProductModel> allList = [];
  List<ProductModel> consumableList = [];
  List<ProductModel> subscriptionList = [];

  VipSource? _vipFrom;
  ConsumeSource? _consFrom;
  OrderAndRes? _orderModel;
  OrderIosRes? _iosOrder;
  bool _isUserBuy = false;
  ProductModel? _buyProduct;

  // 初始化 IAP
  void _initIAP() {
    _subscription = _inAppPurchase.purchaseStream.listen(
      (purchaseDetailsList) => _processPurchaseDetails(purchaseDetailsList),
      onError: (error) => log.e('[iap] 购买监听错误: $error'),
    );
    initStoreInfo();
  }

  Future<void> initStoreInfo() async {
    if (Platform.isIOS) {
      var iosPlatformAddition = _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      await iosPlatformAddition.setDelegate(ExamplePaymentQueueDelegate());
    }
  }

  // 查询产品详情
  Future<void> queryProducts() async {
    if (subscriptionList.isNotEmpty && consumableList.isNotEmpty) {
      return;
    }

    if (!await _isAvailable()) return;

    // iOS 平台特定逻辑
    await _finishTransaction();

    await _getProductIds();
    var ids = _consumableIds.union(_subscriptionIds);
    log.d('[iap] ids: $ids');

    final response = await _inAppPurchase.queryProductDetails(ids);
    if (response.notFoundIDs.isNotEmpty) {
      log.e('[iap] notFoundIDs: ${response.notFoundIDs}');
    }

    for (final productDetails in response.productDetails) {
      final sku = allList.firstWhereOrNull((e) => e.sku == productDetails.id);
      if (sku != null) {
        sku.productDetails = productDetails;
      }
    }

    // 根据 sku.orderNum 从小到大排序
    consumableList = allList.where((sku) => _consumableIds.contains(sku.sku)).toList()
      ..sort((a, b) => (a.orderNum ?? 0).compareTo(b.orderNum ?? 0));

    subscriptionList = allList.where((sku) => _subscriptionIds.contains(sku.sku)).toList()
      ..sort((a, b) => (a.orderNum ?? 0).compareTo(b.orderNum ?? 0));
  }

  Future _getProductIds() async {
    final skus = await IP.fetchSku();
    allList = skus ?? [];

    _consumableIds = allList.where((e) => e.skuType == 0 && e.shelf == true).map((e) => e.sku ?? '').toSet();
    _subscriptionIds = allList.where((e) => e.skuType != 0 && e.shelf == true).map((e) => e.sku ?? '').toSet();
  }

  Future<void> buy(ProductModel model, {VipSource? vipFrom, ConsumeSource? consFrom}) async {
    try {
      SmartDialog.showLoading();
      await _finishTransaction();
      _vipFrom = vipFrom;
      _consFrom = consFrom;
      _isUserBuy = true;
      _buyProduct = model;

      var productDetails = model.productDetails;
      if (productDetails == null) {
        SmartDialog.dismiss();
        SmartDialog.showToast(LocaleKeys.no_available_products.tr);
        return;
      }

      await _createOrder(productDetails);

      final purchaseParam = PurchaseParam(productDetails: productDetails);
      final isConsumable = _consumableIds.contains(productDetails.id);

      await (isConsumable
          ? _inAppPurchase.buyConsumable(purchaseParam: purchaseParam)
          : _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam));
    } catch (e) {
      await SmartDialog.dismiss();
      SmartDialog.showNotify(msg: e.toString(), notifyType: NotifyType.error);
      log.e('[iap] buy error: $e');
    }
  }

  // 恢复购买
  Future<void> restore({bool isNeedShowLoading = true}) async {
    if (!await _isAvailable()) return;

    _isUserBuy = true;
    await _inAppPurchase.restorePurchases();
  }

  // 处理购买详情
  Future<void> _processPurchaseDetails(List<PurchaseDetails> purchaseDetailsList) async {
    if (purchaseDetailsList.isEmpty) return;

    // 按交易日期降序排序
    purchaseDetailsList.sort(
        (a, b) => (int.tryParse(b.transactionDate ?? '0') ?? 0).compareTo(int.tryParse(a.transactionDate ?? '0') ?? 0));

    final first = purchaseDetailsList.first;

    for (var purchaseDetails in purchaseDetailsList) {
      switch (purchaseDetails.status) {
        case PurchaseStatus.purchased:
          if (first.purchaseID == purchaseDetails.purchaseID) {
            await _handleSuccessfulPurchase(purchaseDetails);
          }
          break;
        case PurchaseStatus.restored:
          await _handleSuccessfulPurchase(purchaseDetails);
          break;

        case PurchaseStatus.error:
        case PurchaseStatus.canceled:
          _handlePurchaseError(purchaseDetails);
          break;

        case PurchaseStatus.pending:
          SmartDialog.showLoading();
          break;
      }

      final shouldComplete = purchaseDetails.pendingCompletePurchase ||
          (Platform.isIOS && purchaseDetails.status != PurchaseStatus.pending);

      if (shouldComplete) {
        try {
          await _inAppPurchase.completePurchase(purchaseDetails);
          log.d('[iap] completePurchase done: ${purchaseDetails.productID}');
        } catch (e) {
          log.e('[iap] completePurchase failed: $e');
        }
      }
    }
  }

  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    log.d(' _handleSuccessfulPurchase status: ${purchaseDetails.status}');
    log.d(
        '[iap] _handleSuccessfulPurchase: ${purchaseDetails.productID}, ${purchaseDetails.purchaseID}, ${purchaseDetails.transactionDate}');
    if (!_isUserBuy) {
      log.e('[iap] _handleSuccessfulPurchase error: User did not initiate the purchase');
      SmartDialog.showToast('User did not initiate the purchase!');
      return;
    }

    if (await _verifyAndCompletePurchase(purchaseDetails)) {
      await _markPurchaseAsProcessed(purchaseDetails.purchaseID);
    } else {
      log.e('[iap] _verifyAndCompletePurchase error: ${purchaseDetails.productID}');
      SmartDialog.showNotify(msg: LocaleKeys.verify_purchase_error.tr, notifyType: NotifyType.error);
    }
    _isUserBuy = false;
    _buyProduct = null;
  }

  void _handlePurchaseError(PurchaseDetails purchaseDetails) {
    final error = purchaseDetails.error;
    log.e(error.toString());
    _handleError(
      IAPError(
        source: error?.source ?? '',
        code: error?.code ?? '',
        message: purchaseDetails.status.name,
      ),
    );
  }

  Future<bool> _verifyAndCompletePurchase(PurchaseDetails purchaseDetails) async {
    bool isValid = await verifyPurchaseWithServer(purchaseDetails);
    SmartDialog.dismiss();
    if (isValid) {
      _reportPurchase(purchaseDetails);
      UserHelper().getUserInfo();
    }
    return isValid;
  }

  Future<bool> verifyPurchaseWithServer(PurchaseDetails purchaseDetails) async {
    if (Platform.isIOS) return await _verifyApple(purchaseDetails);
    if (Platform.isAndroid) return await _verifyGoogle(purchaseDetails);
    return false;
  }

  Future<bool> _verifyApple(PurchaseDetails purchaseDetails) async {
    try {
      var receipt = purchaseDetails.verificationData.serverVerificationData;
      if (receipt.isEmpty) {
        final iosPlatformAddition = InAppPurchase.instance.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
        PurchaseVerificationData? verificationData = await iosPlatformAddition.refreshPurchaseVerificationData();
        receipt = verificationData?.serverVerificationData ?? '';
      }
      if (receipt.isEmpty) {
        SmartDialog.showToast('${LocaleKeys.verify_purchase_error.tr} receipt is empty');
        throw Exception('_verifyApple error: receipt is empty');
      }

      log.d('[iap] receipt: $receipt');
      log.d('[iap] purchaseID: ${purchaseDetails.purchaseID}, transactionDate:${purchaseDetails.transactionDate}');

      var result = await IP.verifyIosOrder(
        receipt: receipt,
        skuId: purchaseDetails.productID,
        transactionId: purchaseDetails.purchaseID!,
        purchaseDate: purchaseDetails.transactionDate!,
        orderId: _iosOrder?.id ?? 0,
      );
      return result;
    } catch (e) {
      SmartDialog.showToast(e.toString());
      _handleError(IAPError(source: '', code: '400', message: e.toString()));
      return false;
    } finally {
      _iosOrder = null;
    }
  }

  Future<bool> _verifyGoogle(PurchaseDetails purchaseDetails) async {
    try {
      final googleDetail = purchaseDetails as GooglePlayPurchaseDetails;
      final result = await IP.verifyAndOrder(
        originalJson: googleDetail.billingClientPurchase.originalJson,
        purchaseToken: googleDetail.billingClientPurchase.purchaseToken,
        skuId: purchaseDetails.productID,
        orderType: _subscriptionIds.contains(purchaseDetails.productID) ? 'SUBSCRIPTION' : 'GEMS',
        orderId: _orderModel?.orderNo ?? '',
      );

      _orderModel = null;
      return result;
    } catch (e) {
      _handleError(IAPError(source: '', code: '400', message: e.toString()));
      return false;
    }
  }

  Future<void> _createOrder(ProductDetails productDetails) async {
    final orderType = _consumableIds.contains(productDetails.id) ? 'GEMS' : 'SUBSCRIPTION';

    if (Platform.isIOS) {
      try {
        final order = await IP.makeIosOrder(orderType: orderType, skuId: productDetails.id);
        if (order == null || order.id == null) throw Exception('Creat order error');
        _iosOrder = order;
      } catch (e) {
        SmartDialog.showToast('${LocaleKeys.create_order_error.tr} $e');
        rethrow;
      }
    }
    if (Platform.isAndroid) {
      try {
        final order = await IP.makeAndOrder(orderType: orderType, skuId: productDetails.id);
        if (order == null || order.orderNo == null) throw Exception('Creat order error');

        _orderModel = order;
      } catch (e) {
        SmartDialog.showToast('${LocaleKeys.create_order_error.tr} $e');
        rethrow;
      }
    }
  }

  Future _finishTransaction() async {
    // iOS 平台特定逻辑
    if (Platform.isIOS) {
      // 清理挂起的交易
      final transactions = await SKPaymentQueueWrapper().transactions();
      for (var transaction in transactions) {
        await SKPaymentQueueWrapper().finishTransaction(transaction);
      }
    }
  }

  // 工具方法：标记购买为已处理
  Future<void> _markPurchaseAsProcessed(String? purchaseID) async {
    if (purchaseID != null) await _storage.write(key: purchaseID, value: 'processed');
  }

  Future<bool> _isPurchaseProcessed(String? purchaseID) async {
    if (purchaseID == null) return false;
    final value = await _storage.read(key: purchaseID);
    return value == 'processed';
  }

  Future<bool> _isAvailable() async {
    final isAvailable = await _inAppPurchase.isAvailable();
    if (!isAvailable) {
      SmartDialog.showNotify(msg: LocaleKeys.iap_not_support.tr, notifyType: NotifyType.error);
    }
    return isAvailable;
  }

  void _reportPurchase(PurchaseDetails purchaseDetails) {
    log.d('[iap] _reportPurchase: ${purchaseDetails.productID}');
    final id = purchaseDetails.productID;
    var path = '';
    var from = '';
    if (_consumableIds.contains(id)) {
      path = 'gems';
      from = _consFrom?.name ?? '';
      logEvent('suc_gems');
      final name = 'suc_${path}_${id}_$from';
      log.d('[iap] report: $name');
      logEvent(name);
      _showRechargeSuccess(id);
    } else {
      path = 'sub';
      from = _vipFrom?.name ?? '';
      logEvent('suc_sub');
      final name = 'suc_${path}_${id}_$from';
      log.d('[iap] report: $name');
      logEvent(name);
      _handleVipSuccess();
    }
  }

  void _handleVipSuccess() {
    if (_vipFrom == VipSource.dailyrd) {
      _dailyrdSubSuccess();
    } else {
      SmartDialog.showNotify(msg: LocaleKeys.subscription_successful.tr, notifyType: NotifyType.success);
      Get.back();
    }
  }

  void _dailyrdSubSuccess() async {
    SmartDialog.showLoading();
    await IP.getDailyReward();
    await UserHelper().getUserInfo();
    SmartDialog.dismiss(status: SmartStatus.loading);

    await SmartDialog.showNotify(msg: LocaleKeys.subscription_successful.tr, notifyType: NotifyType.success);
    SmartDialog.dismiss(tag: DialogTag.sigin.name);
    Get.back();
  }

  void _showRechargeSuccess(String productID) {
    logEvent('t_suc_gems');
    final text = '+${numberPart(productID)}';
    IDialog.showRechargeSucc(text);
  }

  void _handleError(IAPError error) {
    SmartDialog.dismiss();
    log.e('[iap] 错误: ${error.message}');
    SmartDialog.showNotify(msg: error.message, notifyType: NotifyType.error);
  }

  void dispose() {
    _subscription?.cancel();
    _subscription = null;
  }
}

/// Example implementation of the
/// [`SKPaymentQueueDelegate`](https://developer.apple.com/documentation/storekit/skpaymentqueuedelegate?language=objc).
///
/// The payment queue delegate can be implementated to provide information
/// needed to complete transactions.
class ExamplePaymentQueueDelegate implements SKPaymentQueueDelegateWrapper {
  @override
  bool shouldContinueTransaction(SKPaymentTransactionWrapper transaction, SKStorefrontWrapper storefront) {
    return true;
  }

  @override
  bool shouldShowPriceConsent() {
    return false;
  }
}
