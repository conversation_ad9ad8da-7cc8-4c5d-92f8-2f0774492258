import 'package:bushy/data/user.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/service/i_p.dart';
import 'package:get/get.dart';

class UserHelper {
  static final UserHelper _instance = UserHelper._internal();

  UserHelper._internal();

  factory UserHelper() {
    return _instance;
  }

  final balance = 0.obs;
  final isVip = false.obs;
  final unreadCount = 0.obs;
  final autoTranslate = false.obs;
  final localChanged = 0.obs;

  User? _user;

  User? get user => _user;

  Future<User?> register() async {
    final cacheUser = ICache().user;
    if (cacheUser != null) {
      _user = cacheUser;
      return cacheUser;
    }

    var user = await IP.register();
    cacheUserInfo(user);
    return user;
  }

  Future<User?> getUserInfo() async {
    final cacheUser = ICache().user;
    if (cacheUser == null) {
      await register();
    }
    var user = await IP.getUserInfo();
    cacheUserInfo(user);
    return user;
  }

  Future updateUser(String nickname) async {
    final id = _user?.id;
    if (id == null) {
      return _user;
    }
    try {
      final body = {
        'id': id,
        'nickname': nickname,
      };
      final result = await IP.updateUserInfo(body);
      if (result) {
        user?.nickname = nickname;
        cacheUserInfo(user);
      }
    } catch (e) {
      log.e('updateUser error: $e');
    }
  }

  void cacheUserInfo(User? user) {
    if (user == null) {
      return;
    }
    _user = user;
    ICache().user = user;
    balance.value = user.gms ?? 0;
    isVip.value = (user.subscriptionEnd ?? 0) > DateTime.now().millisecondsSinceEpoch;
    autoTranslate.value = user.autoTranslate ?? false;
  }

  bool isBalanceEnough(ConsumeSource from) {
    return balance.value >= from.gems;
  }

  Future<void> consume(ConsumeSource from) async {
    try {
      final result = await IP.consumeReq(from.gems, from.name);
      balance.value = result;
    } catch (e) {
      log.e('$e');
    }
  }
}
