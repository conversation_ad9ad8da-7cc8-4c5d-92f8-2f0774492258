import 'dart:io';

import 'package:adjust_sdk/adjust.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

class InfoHeper {
  static InfoHeper? _instance;

  factory InfoHeper() {
    return _instance ??= InfoHeper._internal();
  }

  InfoHeper._internal();

  PackageInfo? _packageInfo;

  Future<PackageInfo> getPackageInfo() async {
    if (_packageInfo != null) {
      return _packageInfo!;
    }
    _packageInfo = await PackageInfo.fromPlatform();
    return _packageInfo!;
  }

  Future<String> version() async {
    return (await getPackageInfo()).version;
  }

  Future<String> buildNumber() async {
    return (await getPackageInfo()).buildNumber;
  }

  Future<String> packageName() async {
    return (await getPackageInfo()).packageName;
  }

  Future<String> getIdfa() async {
    if (!Platform.isIOS) {
      return '';
    }
    final status = await AppTrackingTransparency.trackingAuthorizationStatus;

    if (status == TrackingStatus.notDetermined) {
      await Future.delayed(const Duration(milliseconds: 200));
      await AppTrackingTransparency.requestTrackingAuthorization();
    }
    final uuid = await AppTrackingTransparency.getAdvertisingIdentifier();
    print('uuid: $uuid');
    return uuid;
  }

  // 获取idfv
  Future<String> getIdfv() async {
    if (!Platform.isIOS) {
      return '';
    }
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    final iosInfo = await deviceInfo.iosInfo;
    return iosInfo.identifierForVendor ?? '';
  }

  // device_model
  Future<String> getDeviceModel() async {
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.model;
    }
    if (Platform.isIOS) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.utsname.machine;
    }
    return '';
  }

  // 手机厂商
  Future<String> getDeviceManufacturer() async {
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.manufacturer;
    }
    if (Platform.isIOS) {
      return 'Apple';
    }
    return '';
  }

  // 操作系统版本
  Future<String> getOsVersion() async {
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.release;
    }
    if (Platform.isIOS) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.systemVersion;
    }
    return '';
  }

  Future<bool> isLimitAdTrackingEnabled() async {
    if (Platform.isIOS) {
      final attStatus = await Adjust.getAppTrackingAuthorizationStatus();
      return attStatus == 0 || attStatus == 1; // 0=未决定,1=限制跟踪
    } else if (Platform.isAndroid) {
      final isLimitAdTracking = await Adjust.isEnabled();
      return !isLimitAdTracking; // Android返回的是是否启用跟踪，取反得到是否限制
    }
    return false;
  }
}
