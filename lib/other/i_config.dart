import 'dart:io';

import 'package:adjust_sdk/adjust.dart';
import 'package:adjust_sdk/adjust_config.dart';
import 'package:android_id/android_id.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../data/clothing_data.dart';
import '../data/gift.dart';
import '../data/level_config.dart';
import '../service/i_p.dart';
import 'i_cache.dart';
import 'info_helper.dart';
import 'log_util.dart';

class IConfig {
  static final IConfig _instance = IConfig._internal();

  factory IConfig() => _instance;

  IConfig._internal() {
    _initializePlatformSpecificConfig();
  }

  // 统一配置
  String platform = '';

  // 是否为调试模式
  final bool isDebug = true;
  final String baseUrl = "https://liuhaipeng3.powerfulclean.net/";

  // final bool isDebug = false;
  // final String baseUrl = "https://server.aibushy.com";

  int maxFreeChatCount = 50;
  String? emailCodeRegex;

  List<Map<String, dynamic>> levelList = [
    {'icon': '👋', 'text': 'Level 1 Reward', 'level': 1},
    {'icon': '🥱', 'text': 'Level 2 Reward', 'level': 2},
    {'icon': '😊', 'text': 'Level 3 Reward', 'level': 3},
    {'icon': '💓', 'text': 'Level 4 Reward', 'level': 4},
  ];

  List<LevelConfig> chatLevelConfigs = [];

  void _initializePlatformSpecificConfig() {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        _initAndroidConfig();
        break;
      case TargetPlatform.iOS:
        _initIOSConfig();
        break;
      default:
        break;
    }
  }

  // Android 配置
  void _initAndroidConfig() {
    platform = 'bushy-android';
  }

  // iOS 配置
  void _initIOSConfig() {
    platform = 'bushy';
  }

  // Adjust 初始化
  Future<void> initAdjust() async {
    try {
      String deviceId = await ICache().gDID(o: true);
      String appToken = '9es8jfgysa9s';
      AdjustEnvironment env = AdjustEnvironment.production;

      AdjustConfig config = AdjustConfig(appToken, env)
        ..logLevel = AdjustLogLevel.error
        ..externalDeviceId = deviceId;

      Adjust.initSdk(config);
      log.d('[Adjust]: initializing ✅');
    } catch (e) {
      log.e('[Adjust] catch: $e');
    }
  }

  // Firebase 初始化
  Future<void> initFirebase() async {
    try {
      FirebaseApp app = await Firebase.initializeApp();
      log.d('[Firebase]: Initialized ✅ app: ${app.name}');
      FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
      FirebaseRemoteConfig.instance.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(minutes: 1),
        ),
      );

      await FirebaseRemoteConfig.instance.fetchAndActivate();
      listenRemoteConfig();
    } catch (e) {
      log.e('[Firebase]: catch : $e');
    }
  }

  // 监听 Firebase 配置更新
  Future<void> listenRemoteConfig() async {
    FirebaseRemoteConfig.instance.onConfigUpdated.listen((_) async {
      await FirebaseRemoteConfig.instance.activate();
      _refreshRemoteConfig();
    });
  }

  // 拉取并更新 Firebase 配置
  Future<void> fetchConfig() async {
    try {
      // 确保Firebase已初始化
      if (Firebase.apps.isEmpty) {
        await initFirebase();
      }
      _refreshRemoteConfig();
    } catch (e) {
      log.e('Error fetching remote config: $e');
    }
  }

  // 更新配置
  Future<void> _refreshRemoteConfig() async {
    final remoteConfig = FirebaseRemoteConfig.instance;

    emailCodeRegex = _getConfigValue('email_code_regex', remoteConfig.getString, null);
    maxFreeChatCount = _getConfigValue('max_free_chat_count', remoteConfig.getInt, 50);

    log.d('[FirebaseRemoteConfig] email_code_regex: $emailCodeRegex');
    log.d('[FirebaseRemoteConfig] max_free_chat_count: $maxFreeChatCount');
  }

  // 获取配置值
  T _getConfigValue<T>(String key, T Function(String) fetcher, T defaultValue) {
    final value = fetcher(key);
    if ((value is String && value.isNotEmpty) || (value is int && value != 0)) {
      return value;
    }
    return defaultValue;
  }

  // 请求点击事件
  Future<void> requstBloom({int count = 1}) async {
    if (isDebug) {
      ICache().isBusy = true;
      return;
    }
    if (count > 0) {
      if (ICache().isBusy) return;
    }

    try {
      if (Platform.isIOS) {
        await _requestIosClk();
      } else if (Platform.isAndroid) {
        await _requestAndroidClk();
      }
    } catch (e) {
      log.e('Error in requesClk: $e');
    }
  }

  // iOS 点击事件请求
  Future<void> _requestIosClk() async {
    try {
      final deviceId = await ICache().gDID(o: true);
      final version = await InfoHeper().version();
      final idfa = await InfoHeper().getIdfa();

      final Map<String, dynamic> body = {
        'homeobox': 'com.bushyai.chat',
        'niobium': 'bound',
        'krill': version,
        'glisten': deviceId,
        'stokes': DateTime.now().millisecondsSinceEpoch,
        'gauntlet': idfa,
      };

      final client = GetConnect(timeout: const Duration(seconds: 60));

      final response = await client.post('https://slough.bushyai.com/ceramic/tide/bulletin', body);
      log.i('Response: $body\n ${response.body}');

      if (response.isOk && response.body == 'write') {
        ICache().isBusy = true;
      } else {
        ICache().isBusy = false;
      }
    } catch (e) {
      log.e('Error in _requestIosClk: $e');
    }
  }

  // Android 点击事件请求
  Future<void> _requestAndroidClk() async {
    try {
      final deviceId = await ICache().gDID(o: true);
      final version = await InfoHeper().version();
      final adid = await const AndroidId().getId();

      final Map<String, dynamic> body = {
        'culpa': 'com.blushai.meet',
        'clerk': 'lillian',
        'opinion': version,
        'census': deviceId,
        'hydrous': DateTime.now().millisecondsSinceEpoch,
        'figurate': adid,
        'blank': deviceId,
      };

      final client = GetConnect(timeout: const Duration(seconds: 60));
      log.d('Sending post request: $body');

      final response = await client.post('https://papyri.bushyai.com/sardonic/specific/sumac', body);
      log.i('Response: ${response.body}');

      if (response.isOk && response.body == 'rave') {
        ICache().isBusy = true;
      }
    } catch (e) {
      log.e('Error in _requestAndroidClk: $e');
    }
  }

  // 获取聊天等级配置
  Future<List<LevelConfig>> reqCLC() async {
    if (chatLevelConfigs.isNotEmpty) return chatLevelConfigs;

    final datas = await IP.getChatLevelConfig();
    if (datas != null) {
      chatLevelConfigs = datas;
    }
    return chatLevelConfigs;
  }

  // 加载礼物和服装配置
  Future<void> loadGifts() async {
    gifts = await IP.getGiftConfig();
    clothings = await IP.getChangeConfig();
  }

  List<Gift>? gifts;
  List<ClothingData>? clothings;
}
