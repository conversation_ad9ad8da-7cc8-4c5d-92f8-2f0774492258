import 'package:bushy/other/fb_sdk_util.dart';
import 'package:bushy/other/log_util.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

class NetObs extends GetxService {
  static NetObs get to => Get.find();

  var isConnected = false.obs;

  Future<NetObs> init() async {
    // 初始检查连接状态
    final initialStatus = await Connectivity().checkConnectivity();
    isConnected.value = initialStatus.where((element) => element != ConnectivityResult.none).isNotEmpty;

    // 监听连接状态变化
    Connectivity().onConnectivityChanged.listen((status) async {
      final wasConnected = isConnected.value;
      final nowConnected = status.where((element) => element != ConnectivityResult.none).isNotEmpty;

      isConnected.value = nowConnected;
      log.d('网络连接状态变化：$wasConnected -> $nowConnected');

      // 如果网络从断开状态恢复连接，尝试重新初始化Facebook SDK
      if (!wasConnected && nowConnected) {
        log.d('网络已恢复连接，检查Facebook SDK初始化状态');
        try {
          FBSDKUtil.retryInitializationIfNeeded();
        } catch (e, s) {
          log.e('Facebook SDK 初始化重试失败\nError: $e\nStackTrace: $s');
        }
      }
    });
    return this;
  }
}
