/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/new_ui
  $AssetsImagesNewUiGen get newUi => const $AssetsImagesNewUiGen();

  /// File path: assets/images/pause.svg
  String get pause => 'assets/images/pause.svg';

  /// File path: assets/images/play.svg
  String get play => 'assets/images/play.svg';

  /// List of all assets
  List<String> get values => [pause, play];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/audio.json
  String get audio => 'assets/lottie/audio.json';

  /// File path: assets/lottie/gift_loading.json
  String get giftLoading => 'assets/lottie/gift_loading.json';

  /// File path: assets/lottie/level_up.json
  String get levelUp => 'assets/lottie/level_up.json';

  /// List of all assets
  List<String> get values => [audio, giftLoading, levelUp];
}

class $AssetsImagesNewUiGen {
  const $AssetsImagesNewUiGen();

  /// File path: assets/images/new_ui/accept.png
  AssetGenImage get accept =>
      const AssetGenImage('assets/images/new_ui/accept.png');

  /// File path: assets/images/new_ui/audio_playing.png
  AssetGenImage get audioPlaying =>
      const AssetGenImage('assets/images/new_ui/audio_playing.png');

  /// File path: assets/images/new_ui/audio_stop.png
  AssetGenImage get audioStop =>
      const AssetGenImage('assets/images/new_ui/audio_stop.png');

  /// File path: assets/images/new_ui/bananer.png
  AssetGenImage get bananer =>
      const AssetGenImage('assets/images/new_ui/bananer.png');

  /// File path: assets/images/new_ui/be_vip_bg.png
  AssetGenImage get beVipBg =>
      const AssetGenImage('assets/images/new_ui/be_vip_bg.png');

  /// File path: assets/images/new_ui/be_vip_role.png
  AssetGenImage get beVipRole =>
      const AssetGenImage('assets/images/new_ui/be_vip_role.png');

  /// File path: assets/images/new_ui/bnbi_chat.svg
  String get bnbiChat => 'assets/images/new_ui/bnbi_chat.svg';

  /// File path: assets/images/new_ui/bnbi_creat.svg
  String get bnbiCreat => 'assets/images/new_ui/bnbi_creat.svg';

  /// File path: assets/images/new_ui/bnbi_home.svg
  String get bnbiHome => 'assets/images/new_ui/bnbi_home.svg';

  /// File path: assets/images/new_ui/bnbi_moments.svg
  String get bnbiMoments => 'assets/images/new_ui/bnbi_moments.svg';

  /// File path: assets/images/new_ui/cate_anime.png
  AssetGenImage get cateAnime =>
      const AssetGenImage('assets/images/new_ui/cate_anime.png');

  /// File path: assets/images/new_ui/cate_dressup.png
  AssetGenImage get cateDressup =>
      const AssetGenImage('assets/images/new_ui/cate_dressup.png');

  /// File path: assets/images/new_ui/cate_real.png
  AssetGenImage get cateReal =>
      const AssetGenImage('assets/images/new_ui/cate_real.png');

  /// File path: assets/images/new_ui/cate_video.png
  AssetGenImage get cateVideo =>
      const AssetGenImage('assets/images/new_ui/cate_video.png');

  /// File path: assets/images/new_ui/check.png
  AssetGenImage get check =>
      const AssetGenImage('assets/images/new_ui/check.png');

  /// File path: assets/images/new_ui/check_no.png
  AssetGenImage get checkNo =>
      const AssetGenImage('assets/images/new_ui/check_no.png');

  /// File path: assets/images/new_ui/choose.png
  AssetGenImage get choose =>
      const AssetGenImage('assets/images/new_ui/choose.png');

  /// File path: assets/images/new_ui/clear.svg
  String get clear => 'assets/images/new_ui/clear.svg';

  /// File path: assets/images/new_ui/close_white.png
  AssetGenImage get closeWhite =>
      const AssetGenImage('assets/images/new_ui/close_white.png');

  /// File path: assets/images/new_ui/creat_bg.png
  AssetGenImage get creatBg =>
      const AssetGenImage('assets/images/new_ui/creat_bg.png');

  /// File path: assets/images/new_ui/filtter.png
  AssetGenImage get filtter =>
      const AssetGenImage('assets/images/new_ui/filtter.png');

  /// File path: assets/images/new_ui/fire.svg
  String get fire => 'assets/images/new_ui/fire.svg';

  /// File path: assets/images/new_ui/follow.png
  AssetGenImage get follow =>
      const AssetGenImage('assets/images/new_ui/follow.png');

  /// File path: assets/images/new_ui/followed.png
  AssetGenImage get followed =>
      const AssetGenImage('assets/images/new_ui/followed.png');

  /// File path: assets/images/new_ui/gift.svg
  String get gift => 'assets/images/new_ui/gift.svg';

  /// File path: assets/images/new_ui/gold.png
  AssetGenImage get gold =>
      const AssetGenImage('assets/images/new_ui/gold.png');

  /// File path: assets/images/new_ui/gold3.png
  AssetGenImage get gold3 =>
      const AssetGenImage('assets/images/new_ui/gold3.png');

  /// File path: assets/images/new_ui/hangup.png
  AssetGenImage get hangup =>
      const AssetGenImage('assets/images/new_ui/hangup.png');

  /// File path: assets/images/new_ui/input_search.png
  AssetGenImage get inputSearch =>
      const AssetGenImage('assets/images/new_ui/input_search.png');

  /// File path: assets/images/new_ui/loading.png
  AssetGenImage get loading =>
      const AssetGenImage('assets/images/new_ui/loading.png');

  /// File path: assets/images/new_ui/lock.png
  AssetGenImage get lock =>
      const AssetGenImage('assets/images/new_ui/lock.png');

  /// File path: assets/images/new_ui/mic_off.png
  AssetGenImage get micOff =>
      const AssetGenImage('assets/images/new_ui/mic_off.png');

  /// File path: assets/images/new_ui/mic_on.png
  AssetGenImage get micOn =>
      const AssetGenImage('assets/images/new_ui/mic_on.png');

  /// File path: assets/images/new_ui/msg.png
  AssetGenImage get msg => const AssetGenImage('assets/images/new_ui/msg.png');

  /// File path: assets/images/new_ui/nav_back.png
  AssetGenImage get navBack =>
      const AssetGenImage('assets/images/new_ui/nav_back.png');

  /// File path: assets/images/new_ui/nav_close.png
  AssetGenImage get navClose =>
      const AssetGenImage('assets/images/new_ui/nav_close.png');

  /// File path: assets/images/new_ui/nav_close_black.png
  AssetGenImage get navCloseBlack =>
      const AssetGenImage('assets/images/new_ui/nav_close_black.png');

  /// File path: assets/images/new_ui/nav_qa.png
  AssetGenImage get navQa =>
      const AssetGenImage('assets/images/new_ui/nav_qa.png');

  /// File path: assets/images/new_ui/no_chat.png
  AssetGenImage get noChat =>
      const AssetGenImage('assets/images/new_ui/no_chat.png');

  /// File path: assets/images/new_ui/no_net.png
  AssetGenImage get noNet =>
      const AssetGenImage('assets/images/new_ui/no_net.png');

  /// File path: assets/images/new_ui/no_search_result.png
  AssetGenImage get noSearchResult =>
      const AssetGenImage('assets/images/new_ui/no_search_result.png');

  /// File path: assets/images/new_ui/phone.svg
  String get phone => 'assets/images/new_ui/phone.svg';

  /// File path: assets/images/new_ui/play.png
  AssetGenImage get play =>
      const AssetGenImage('assets/images/new_ui/play.png');

  /// File path: assets/images/new_ui/play_black.png
  AssetGenImage get playBlack =>
      const AssetGenImage('assets/images/new_ui/play_black.png');

  /// File path: assets/images/new_ui/rate_bg.png
  AssetGenImage get rateBg =>
      const AssetGenImage('assets/images/new_ui/rate_bg.png');

  /// File path: assets/images/new_ui/rate_img.png
  AssetGenImage get rateImg =>
      const AssetGenImage('assets/images/new_ui/rate_img.png');

  /// File path: assets/images/new_ui/report.svg
  String get report => 'assets/images/new_ui/report.svg';

  /// File path: assets/images/new_ui/search.png
  AssetGenImage get search =>
      const AssetGenImage('assets/images/new_ui/search.png');

  /// File path: assets/images/new_ui/send.svg
  String get send => 'assets/images/new_ui/send.svg';

  /// File path: assets/images/new_ui/setting.png
  AssetGenImage get setting =>
      const AssetGenImage('assets/images/new_ui/setting.png');

  /// File path: assets/images/new_ui/sp_bg.png
  AssetGenImage get spBg =>
      const AssetGenImage('assets/images/new_ui/sp_bg.png');

  /// File path: assets/images/new_ui/sp_logo.png
  AssetGenImage get spLogo =>
      const AssetGenImage('assets/images/new_ui/sp_logo.png');

  /// File path: assets/images/new_ui/svg_close.svg
  String get svgClose => 'assets/images/new_ui/svg_close.svg';

  /// File path: assets/images/new_ui/tab_chat_d.png
  AssetGenImage get tabChatD =>
      const AssetGenImage('assets/images/new_ui/tab_chat_d.png');

  /// File path: assets/images/new_ui/tab_chat_s.png
  AssetGenImage get tabChatS =>
      const AssetGenImage('assets/images/new_ui/tab_chat_s.png');

  /// File path: assets/images/new_ui/tab_like_d.png
  AssetGenImage get tabLikeD =>
      const AssetGenImage('assets/images/new_ui/tab_like_d.png');

  /// File path: assets/images/new_ui/tab_like_s.png
  AssetGenImage get tabLikeS =>
      const AssetGenImage('assets/images/new_ui/tab_like_s.png');

  /// File path: assets/images/new_ui/tag_clothe.png
  AssetGenImage get tagClothe =>
      const AssetGenImage('assets/images/new_ui/tag_clothe.png');

  /// File path: assets/images/new_ui/tag_img.png
  AssetGenImage get tagImg =>
      const AssetGenImage('assets/images/new_ui/tag_img.png');

  /// File path: assets/images/new_ui/tag_video.png
  AssetGenImage get tagVideo =>
      const AssetGenImage('assets/images/new_ui/tag_video.png');

  /// File path: assets/images/new_ui/trans.svg
  String get trans => 'assets/images/new_ui/trans.svg';

  /// File path: assets/images/new_ui/video_bg.png
  AssetGenImage get videoBg =>
      const AssetGenImage('assets/images/new_ui/video_bg.png');

  /// File path: assets/images/new_ui/vip.png
  AssetGenImage get vip => const AssetGenImage('assets/images/new_ui/vip.png');

  /// File path: assets/images/new_ui/vip_bg_1.png
  AssetGenImage get vipBg1 =>
      const AssetGenImage('assets/images/new_ui/vip_bg_1.png');

  /// File path: assets/images/new_ui/vip_bg_2.png
  AssetGenImage get vipBg2 =>
      const AssetGenImage('assets/images/new_ui/vip_bg_2.png');

  /// List of all assets
  List<dynamic> get values => [
        accept,
        audioPlaying,
        audioStop,
        bananer,
        beVipBg,
        beVipRole,
        bnbiChat,
        bnbiCreat,
        bnbiHome,
        bnbiMoments,
        cateAnime,
        cateDressup,
        cateReal,
        cateVideo,
        check,
        checkNo,
        choose,
        clear,
        closeWhite,
        creatBg,
        filtter,
        fire,
        follow,
        followed,
        gift,
        gold,
        gold3,
        hangup,
        inputSearch,
        loading,
        lock,
        micOff,
        micOn,
        msg,
        navBack,
        navClose,
        navCloseBlack,
        navQa,
        noChat,
        noNet,
        noSearchResult,
        phone,
        play,
        playBlack,
        rateBg,
        rateImg,
        report,
        search,
        send,
        setting,
        spBg,
        spLogo,
        svgClose,
        tabChatD,
        tabChatS,
        tabLikeD,
        tabLikeS,
        tagClothe,
        tagImg,
        tagVideo,
        trans,
        videoBg,
        vip,
        vipBg1,
        vipBg2
      ];
}

class Assets {
  const Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
