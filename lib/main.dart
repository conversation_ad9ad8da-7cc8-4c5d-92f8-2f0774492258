import 'package:bushy/other/i_cache.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'common/s_loading.dart';
import 'generated/locales.g.dart';
import 'other/i_config.dart';
import 'other/i_path.dart';
import 'other/i_theme.dart';
import 'other/log_util.dart';
import 'other/nav_obs.dart';
import 'other/net_obs.dart';
import 'service/i_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 只允许竖屏
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  setupToast();
  setupRefresh();

  await LogUtil.initLogger();

  await GetStorage.init();

  await Get.putAsync<NetObs>(() => NetObs().init());

  IService().init(baseUrl: IConfig().baseUrl);

  IConfig().initAdjust();
  await IConfig().initFirebase();

  runApp(const MyApp());
}

void setupToast() {
  SmartDialog.config.toast = SmartConfigToast(alignment: Alignment.center);
}

void setupRefresh() {
  const textStyle = TextStyle(
    color: IColor.brand,
  );
  EasyRefresh.defaultHeaderBuilder = () => const ClassicHeader(
        textStyle: textStyle,
        messageStyle: textStyle,
        iconTheme: IconThemeData(
          color: IColor.brand,
        ),
        showMessage: false,
        showText: false,
      );
  EasyRefresh.defaultFooterBuilder = () => const ClassicFooter(
        textStyle: textStyle,
        messageStyle: textStyle,
        iconTheme: IconThemeData(
          color: IColor.brand,
        ),
        showMessage: false,
        showText: false,
      );
}

Iterable<Locale> supportedLocales = const [
  Locale('en', 'US'), // 英语（美国）
  Locale('ar', 'SA'), // 阿拉伯语（沙特阿拉伯）
  Locale('fr', 'FR'), // 法语（法国）
  Locale('de', 'DE'), // 德语（德国）
  Locale('es', 'ES'), // 西班牙语（西班牙）
  Locale('pt', 'BR'), // 葡萄牙语（巴西）
  Locale('pt', 'PT'), // 葡萄牙语（葡萄牙）
  Locale('ja', 'JP'), // 日语（日本）
  Locale('ko', 'KR'), // 韩语（韩国）
  Locale('it', 'IT'), // 意大利语（意大利）
  Locale('tr', 'TR'), // 土耳其语（土耳其）
  Locale('vi', 'VN'), // 越南语（越南）
  Locale('id', 'ID'), // 印尼语（印度尼西亚）
  Locale('th', 'TH'), // 泰语（泰国）
  Locale('fil', 'PH'), // 菲律宾语（菲律宾）
  Locale('zh', 'TW'), // 繁体中文（台湾）
];

Locale get locale {
  final cacheLocale = ICache().locale;
  if (cacheLocale != null) {
    for (var s in supportedLocales) {
      if (s == cacheLocale) {
        return s;
      }
    }
  } else {
    final locale = Get.deviceLocale;
    for (var s in supportedLocales) {
      if (s.languageCode == locale?.languageCode) {
        return s;
      }
    }
  }
  return supportedLocales.first;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: (context, child) {
          return GetMaterialApp(
            title: 'Bushy AI',
            debugShowCheckedModeBanner: false,
            initialRoute: IPath.splash,
            theme: ThemeData(
              primaryColor: const Color(0xff906BF7),
              scaffoldBackgroundColor: Colors.white,
              appBarTheme: AppBarTheme(
                elevation: 0,
                backgroundColor: Colors.transparent,
                titleTextStyle: ITStyle.l2sem(IColor.n02),
              ),
            ),
            getPages: IPath.pages,
            themeMode: ThemeMode.light,
            navigatorObservers: [
              FlutterSmartDialog.observer,
              NavObs().observer,
              NavObs().routeObserver,
            ],
            builder: FlutterSmartDialog.init(
              loadingBuilder: (msg) => const SLoading(),
            ),
            // 国际化
            supportedLocales: supportedLocales,
            locale: locale,
            fallbackLocale: const Locale('en', 'US'),
            translationsKeys: AppTranslation.translations,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            localeListResolutionCallback: (locales, supportedLocales) {
              return locales?.firstWhere(
                (l) => supportedLocales.any((s) => s.languageCode == l.languageCode),
                orElse: () => supportedLocales.first,
              );
            },
          );
        },
      ),
    );
  }
}
