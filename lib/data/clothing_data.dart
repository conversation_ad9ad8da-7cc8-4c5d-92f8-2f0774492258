import 'dart:convert';

class ClothingData {
  final int? id;
  final String? kitName;
  final int? kitType;
  final String? img;
  final dynamic cdesc;
  final int? prc;

  ClothingData({
    this.id,
    this.kitName,
    this.kitType,
    this.img,
    this.cdesc,
    this.prc,
  });

  factory ClothingData.fromRawJson(String str) => ClothingData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ClothingData.fromJson(Map<String, dynamic> json) => ClothingData(
        id: json["id"],
        kitName: json["kit_name"],
        kitType: json["kit_type"],
        img: json["img"],
        cdesc: json["cdesc"],
        prc: json["prc"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "kit_name": kitName,
        "kit_type": kitType,
        "img": img,
        "cdesc": cdesc,
        "prc": prc,
      };
}
