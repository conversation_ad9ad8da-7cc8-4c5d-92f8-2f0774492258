import 'dart:convert';

import 'package:bushy/data/clothe_model.dart';
import 'package:get/get.dart';

class ChaterRes {
  final List<Chater>? records;
  final int? total;
  final int? size;
  final int? current;
  final int? pages;

  ChaterRes({
    this.records,
    this.total,
    this.size,
    this.current,
    this.pages,
  });

  factory ChaterRes.fromRawJson(String str) => ChaterRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ChaterRes.fromJson(Map<String, dynamic> json) => ChaterRes(
        records: json["records"] == null ? [] : List<Chater>.from(json["records"]!.map((x) => Chater.fromJson(x))),
        total: json["total"],
        size: json["size"],
        current: json["current"],
        pages: json["pages"],
      );

  Map<String, dynamic> toJson() => {
        "records": records == null ? [] : List<dynamic>.from(records!.map((x) => x.toJson())),
        "total": total,
        "size": size,
        "current": current,
        "pages": pages,
      };
}

class Chater {
  String? id;
  int? age;
  String? aboutMe;
  Media? media;
  String? avatar;
  String? name;
  String? platform;
  String? renderStyle;
  String? likes;
  List<String>? greetings;
  List<GreetingsVoice>? greetingsVoice;
  String? sessionCount;
  bool? vip;
  int? orderNum;
  List<String>? tags;
  String? tagType;
  String? scenario;
  double? temperature;
  String? voiceId;
  String? engine;
  int? gender;
  bool? videoChat;
  List<CharacterVideoChat>? characterVideoChat;
  List<String>? genPhotoTags;
  List<String>? genVideoTags;
  bool? genPhoto;
  bool? genVideo;
  bool? gems;
  bool? collect;
  bool? changeClothing;
  String? lastMessage;
  List<RoleImage>? images;
  List<ClotheModel>? changeClothes;

  CharacterVideoChat? get guide {
    return characterVideoChat?.firstWhereOrNull((e) => e.tag == 'guide');
  }

  Chater(
      {this.id,
      this.age,
      this.aboutMe,
      this.media,
      this.avatar,
      this.name,
      this.platform,
      this.renderStyle,
      this.likes,
      this.greetings,
      this.greetingsVoice,
      this.sessionCount,
      this.vip,
      this.orderNum,
      this.tags,
      this.scenario,
      this.temperature,
      this.voiceId,
      this.engine,
      this.gender,
      this.videoChat,
      this.characterVideoChat,
      this.genPhotoTags,
      this.genVideoTags,
      this.genPhoto,
      this.genVideo,
      this.gems,
      this.collect,
      this.lastMessage,
      this.images,
      this.changeClothes,
      this.changeClothing,
      this.tagType});

  factory Chater.fromRawJson(String str) => Chater.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Chater.fromJson(Map<String, dynamic> json) => Chater(
        id: json["id"],
        age: json["yrs"],
        aboutMe: json["bio"],
        media: json["med"] == null ? null : Media.fromJson(json["med"]),
        avatar: json["avt"],
        name: json["nm"],
        platform: json["plt"],
        renderStyle: json["rnd_stl"],
        likes: json["lk"],
        greetings: json["grt"] == null ? [] : List<String>.from(json["grt"]!.map((x) => x)),
        greetingsVoice: json["gr_voi"] == null
            ? []
            : List<GreetingsVoice>.from(json["gr_voi"]!.map((x) => GreetingsVoice.fromJson(x))),
        sessionCount: json["sess_cnt"],
        vip: json["vip_lvl"],
        orderNum: json["ord_no"],
        tags: json["tg"] == null ? [] : List<String>.from(json["tg"]!.map((x) => x)),
        scenario: json["scenario"],
        temperature: json["temperature"]?.toDouble(),
        voiceId: json["vid"],
        engine: json["eng"],
        gender: json["gndr"],
        videoChat: json["vchat"],
        characterVideoChat: json["char_vchat"] == null
            ? []
            : List<CharacterVideoChat>.from(json["char_vchat"]!.map((x) => CharacterVideoChat.fromJson(x))),
        genPhotoTags: json["gen_ptags"] == null ? [] : List<String>.from(json["gen_ptags"]!.map((x) => x)),
        genVideoTags: json["gen_video_tags"] == null ? [] : List<String>.from(json["gen_video_tags"]!.map((x) => x)),
        genPhoto: json["gen_ph"],
        genVideo: json["gen_vid"],
        gems: json["gms"],
        collect: json["collect"],
        lastMessage: json["last_message"],
        tagType: json["tag_type"],
        changeClothing: json["change_kit"],
        images: json['parts'] == null ? [] : List<RoleImage>.from(json['parts']!.map((x) => RoleImage.fromJson(x))),
        changeClothes: json['change_clothes'] == null
            ? []
            : List<ClotheModel>.from(json['change_clothes']!.map((x) => ClotheModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "yrs": age,
        "bio": aboutMe,
        "med": media?.toJson(),
        "avt": avatar,
        "nm": name,
        "plt": platform,
        "rnd_stl": renderStyle,
        "lk": likes,
        "grt": greetings == null ? [] : List<dynamic>.from(greetings!.map((x) => x)),
        "gr_voi": greetingsVoice == null ? [] : List<dynamic>.from(greetingsVoice!.map((x) => x.toJson())),
        "sess_cnt": sessionCount,
        "vip_lvl": vip,
        "ord_no": orderNum,
        "tg": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "scenario": scenario,
        "temperature": temperature,
        "vid": voiceId,
        "eng": engine,
        "gndr": gender,
        "vchat": videoChat,
        "char_vchat": characterVideoChat == null ? [] : List<dynamic>.from(characterVideoChat!.map((x) => x.toJson())),
        "gen_ptags": genPhotoTags == null ? [] : List<dynamic>.from(genPhotoTags!.map((x) => x)),
        "gen_video_tags": genVideoTags == null ? [] : List<dynamic>.from(genVideoTags!.map((x) => x)),
        "gen_ph": genPhoto,
        "gen_vid": genVideo,
        "gms": gems,
        "collect": collect,
        "last_message": lastMessage,
        "tag_type": tagType,
        "change_kit": changeClothing,
        'parts': images == null ? [] : List<dynamic>.from(images!.map((x) => x.toJson())),
        'change_clothes': changeClothes == null ? [] : List<dynamic>.from(changeClothes!.map((x) => x.toJson())),
      };

  Chater copyWith({
    String? id,
    int? age,
    String? aboutMe,
    Media? media,
    String? avatar,
    String? name,
    String? platform,
    String? renderStyle,
    String? likes,
    List<String>? greetings,
    List<GreetingsVoice>? greetingsVoice,
    String? sessionCount,
    bool? vip,
    int? orderNum,
    List<String>? tags,
    String? scenario,
    double? temperature,
    String? voiceId,
    String? engine,
    int? gender,
    bool? videoChat,
    List<CharacterVideoChat>? characterVideoChat,
    List<String>? genPhotoTags,
    List<String>? genVideoTags,
    bool? genPhoto,
    bool? genVideo,
    bool? gems,
    bool? collect,
    bool? changeClothing,
    String? lastMessage,
    String? tagType,
    List<RoleImage>? images,
    List<ClotheModel>? changeClothes,
  }) {
    return Chater(
      id: id ?? this.id,
      age: age ?? this.age,
      aboutMe: aboutMe ?? this.aboutMe,
      media: media ?? this.media,
      avatar: avatar ?? this.avatar,
      name: name ?? this.name,
      platform: platform ?? this.platform,
      renderStyle: renderStyle ?? this.renderStyle,
      likes: likes ?? this.likes,
      greetings: greetings ?? this.greetings,
      greetingsVoice: greetingsVoice ?? this.greetingsVoice,
      sessionCount: sessionCount ?? this.sessionCount,
      vip: vip ?? this.vip,
      orderNum: orderNum ?? this.orderNum,
      tags: tags ?? this.tags,
      scenario: scenario ?? this.scenario,
      temperature: temperature ?? this.temperature,
      voiceId: voiceId ?? this.voiceId,
      engine: engine ?? this.engine,
      gender: gender ?? this.gender,
      videoChat: videoChat ?? this.videoChat,
      characterVideoChat: characterVideoChat ?? this.characterVideoChat,
      genPhotoTags: genPhotoTags ?? this.genPhotoTags,
      genVideoTags: genVideoTags ?? this.genVideoTags,
      genPhoto: genPhoto ?? this.genPhoto,
      genVideo: genVideo ?? this.genVideo,
      gems: gems ?? this.gems,
      collect: collect ?? this.collect,
      lastMessage: lastMessage ?? this.lastMessage,
      images: images ?? this.images,
      tagType: tagType ?? this.tagType,
      changeClothing: changeClothing ?? this.changeClothing,
      changeClothes: changeClothes ?? this.changeClothes,
    );
  }
}

class CharacterVideoChat {
  final int? id;
  final String? characterId;
  final String? tag;
  final int? duration;
  final String? url;
  final String? gifUrl;

  CharacterVideoChat({
    this.id,
    this.characterId,
    this.tag,
    this.duration,
    this.url,
    this.gifUrl,
  });

  factory CharacterVideoChat.fromRawJson(String str) => CharacterVideoChat.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CharacterVideoChat.fromJson(Map<String, dynamic> json) => CharacterVideoChat(
        id: json["id"],
        characterId: json["char_id"],
        tag: json["tag"],
        duration: json["dur"] is double ? json["dur"].toInt() : json["dur"],
        url: json["uri"],
        gifUrl: json["gif_url"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "char_id": characterId,
        "tag": tag,
        "dur": duration,
        "uri": url,
        "gif_url": gifUrl,
      };
}

class GreetingsVoice {
  final String? url;
  final int? duration;

  GreetingsVoice({
    this.url,
    this.duration,
  });

  factory GreetingsVoice.fromRawJson(String str) => GreetingsVoice.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GreetingsVoice.fromJson(Map<String, dynamic> json) => GreetingsVoice(
        url: json["uri"],
        duration: json["dur"] is double ? json["dur"].toInt() : json["dur"],
      );

  Map<String, dynamic> toJson() => {
        "uri": url,
        "dur": duration,
      };
}

class Media {
  final List<String>? characterImages;

  Media({
    this.characterImages,
  });

  factory Media.fromRawJson(String str) => Media.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        characterImages:
            json["character_images"] == null ? [] : List<String>.from(json["character_images"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "character_images": characterImages == null ? [] : List<dynamic>.from(characterImages!.map((x) => x)),
      };
}

class RoleImage {
  int? id;
  String? imageUrl;
  String? modelId;
  int? gemTally;
  bool? unlocked;

  RoleImage({
    this.id,
    this.imageUrl,
    this.modelId,
    this.gemTally,
    this.unlocked,
  });

  factory RoleImage.fromRawJson(String str) => RoleImage.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RoleImage.fromJson(Map<String, dynamic> json) => RoleImage(
        id: json['id'],
        imageUrl: json['image_url'],
        modelId: json['model_id'],
        gemTally: json['gms'],
        unlocked: json['unlocked'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'image_url': imageUrl,
        'model_id': modelId,
        'gms': gemTally,
        'unlocked': unlocked,
      };

  RoleImage copyWith({
    int? id,
    String? imageUrl,
    String? modelId,
    int? gemTally,
    bool? unlocked,
  }) {
    return RoleImage(
      id: id ?? this.id,
      imageUrl: imageUrl ?? this.imageUrl,
      modelId: modelId ?? this.modelId,
      gemTally: gemTally ?? this.gemTally,
      unlocked: unlocked ?? this.unlocked,
    );
  }
}
