import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:get/get.dart';

enum InputBtnType {
  tease,
  gifts,
  clothing,
}

class InputBtnModel {
  final InputBtnType type;
  final String name;
  final String icon;
  final String color;
  final String textColor;
  final List<String> list;

  InputBtnModel({
    required this.type,
    required this.name,
    required this.icon,
    required this.color,
    required this.textColor,
    required this.list,
  });

  static InputBtnModel tease() {
    var iconPath = Assets.images.newUi.fire;
    const colorHex = "0x80FDF652";
    const textColorHex = "0xff906BF7";

    return InputBtnModel(
      type: InputBtnType.tease,
      name: LocaleKeys.tease.tr,
      icon: iconPath,
      color: colorHex,
      textColor: textColorHex,
      list: [],
    );
  }

  static InputBtnModel gifts() {
    var iconPath = Assets.images.newUi.gift;
    const colorHex = "0x80FDF652";
    const textColorHex = "0xff906BF7";

    return InputBtnModel(
      type: InputBtnType.gifts,
      name: LocaleKeys.gifts.tr,
      icon: iconPath,
      color: colorHex,
      textColor: textColorHex,
      list: [],
    );
  }
}
