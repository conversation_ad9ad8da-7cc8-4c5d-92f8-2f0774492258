import 'dart:convert';

import 'package:in_app_purchase/in_app_purchase.dart';

class ProductModel {
  final String? sku;
  // final double? price;
  final int? number;
  final int? orderNum; // 排序

  /// 默认选中
  final bool? defaultSku;
  final bool? lifetime;

  ///  GEMS(0, "钻石"),   VIP(1, "VIP"), SV<PERSON>(2, "SVIP"),   NOT_VIP(3, "非VIP");
  // final int? skuLevel;
  // GEMS(0, "钻石"), WEEK(1, "周卡"),  MONTH(2, "月卡"), YEAR(3, "年卡"),  LIFETIME(4, "永久订阅"),
  final int? skuType;
  final int? createImg;
  final int? createVideo;

  /// 是否上架
  final bool? shelf;

  ///  @VL(value = "1", label = "Best Value"),
  /// @VL(value = "2", label = "Most Popular"),
  /// @VL(value = "3", label = "\uD83D\uDD25Save 75%")
  final int? tag;

  ProductDetails? productDetails;

  bool get isWeek => skuType == 1;
  bool get isMonth => skuType == 2;
  bool get isYear => skuType == 3;
  bool get isLifeTime => lifetime ?? false;

  ProductModel({
    this.sku,
    this.number,
    this.defaultSku,
    this.lifetime,
    this.skuType,
    this.createImg,
    this.createVideo,
    this.shelf,
    this.tag,
    this.orderNum,
  });

  factory ProductModel.fromRawJson(String str) => ProductModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductModel.fromJson(Map<String, dynamic> json) => ProductModel(
        sku: json["sku"],
        number: json["number"],
        defaultSku: json["default_sku"],
        lifetime: json["lifetime"],
        skuType: json["sku_type"],
        createImg: json["create_img"],
        createVideo: json["create_video"],
        shelf: json["shelf"],
        tag: json["tag"],
        orderNum: json["order_num"],
      );

  Map<String, dynamic> toJson() => {
        "sku": sku,
        "number": number,
        "default_sku": defaultSku,
        "lifetime": lifetime,
        "sku_type": skuType,
        "create_img": createImg,
        "create_video": createVideo,
        "shelf": shelf,
        "tag": tag,
        "order_num": orderNum,
      };
}
