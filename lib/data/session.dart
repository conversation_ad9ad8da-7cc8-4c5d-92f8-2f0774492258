import 'dart:convert';

class SessionRes {
  final int? current;
  final int? pages;
  final List<Session>? records;
  final int? size;
  final int? total;

  SessionRes({
    this.current,
    this.pages,
    this.records,
    this.size,
    this.total,
  });

  factory SessionRes.fromRawJson(String str) => SessionRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SessionRes.fromJson(Map<String, dynamic> json) => SessionRes(
        current: json["current"],
        pages: json["pages"],
        records: json["records"] == null ? [] : List<Session>.from(json["records"]!.map((x) => Session.fromJson(x))),
        size: json["size"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "current": current,
        "pages": pages,
        "records": records == null ? [] : List<dynamic>.from(records!.map((x) => x.toJson())),
        "size": size,
        "total": total,
      };
}

class Session {
  final int? id;
  final String? avatar;
  final String? userId;
  final String? title;
  final bool? pinned;
  final DateTime? pinnedTime;
  final String? characterId;
  final dynamic model;
  final int? templateId;
  final String? voiceModel;
  final String? lastMessage;
  final bool? collect;

  Session({
    this.id,
    this.avatar,
    this.userId,
    this.title,
    this.pinned,
    this.pinnedTime,
    this.characterId,
    this.model,
    this.templateId,
    this.voiceModel,
    this.lastMessage,
    this.collect,
  });

  factory Session.fromRawJson(String str) => Session.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Session.fromJson(Map<String, dynamic> json) => Session(
        id: json["id"],
        avatar: json["avt"],
        userId: json["uid"],
        title: json["title"],
        pinned: json["pinned"],
        pinnedTime: json["pinned_time"] == null ? null : DateTime.parse(json["pinned_time"]),
        characterId: json["char_id"],
        model: json["model"],
        templateId: json["tpl_id"],
        voiceModel: json["voice_model"],
        lastMessage: json["last_message"],
        collect: json["collect"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "avt": avatar,
        "uid": userId,
        "title": title,
        "pinned": pinned,
        "pinned_time": pinnedTime?.toIso8601String(),
        "char_id": characterId,
        "model": model,
        "tpl_id": templateId,
        "voice_model": voiceModel,
        "last_message": lastMessage,
        "collect": collect,
      };
}
