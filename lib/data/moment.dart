import 'dart:convert';

class MomentsRes {
  final List<Moments>? records;
  final int? total;
  final int? size;
  final int? current;
  final int? pages;

  MomentsRes({
    this.records,
    this.total,
    this.size,
    this.current,
    this.pages,
  });

  factory MomentsRes.fromRawJson(String str) => MomentsRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MomentsRes.fromJson(Map<String, dynamic> json) => MomentsRes(
        records: json["records"] == null ? [] : List<Moments>.from(json["records"]!.map((x) => Moments.fromJson(x))),
        total: json["total"],
        size: json["size"],
        current: json["current"],
        pages: json["pages"],
      );

  Map<String, dynamic> toJson() => {
        "records": records == null ? [] : List<dynamic>.from(records!.map((x) => x.toJson())),
        "total": total,
        "size": size,
        "current": current,
        "pages": pages,
      };
}

class Moments {
  final int? id;
  final String? characterAvatar;
  final String? characterId;
  final String? characterName;
  final String? cover;
  final dynamic createTime;
  final int? duration;
  final bool? hideCharacter;
  final bool? istop;
  final String? media;
  final String? mediaText;
  final String? text;
  final dynamic updateTime;

  Moments({
    this.id,
    this.characterAvatar,
    this.characterId,
    this.characterName,
    this.cover,
    this.createTime,
    this.duration,
    this.hideCharacter,
    this.istop,
    this.media,
    this.mediaText,
    this.text,
    this.updateTime,
  });

  factory Moments.fromRawJson(String str) => Moments.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Moments.fromJson(Map<String, dynamic> json) => Moments(
        id: json["id"],
        characterAvatar: json["character_avatar"],
        characterId: json["char_id"],
        characterName: json["char_nm"],
        cover: json["cover"],
        createTime: json["crt_tm"],
        duration: json["dur"],
        hideCharacter: json["hid_char"],
        istop: json["istop"],
        media: json["med"],
        mediaText: json["media_text"],
        text: json["text"],
        updateTime: json["upd_tm"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "character_avatar": characterAvatar,
        "char_id": characterId,
        "char_nm": characterName,
        "cover": cover,
        "crt_tm": createTime,
        "dur": duration,
        "hid_char": hideCharacter,
        "istop": istop,
        "med": media,
        "media_text": mediaText,
        "text": text,
        "upd_tm": updateTime,
      };
}
