import 'dart:convert';

class GenRes {
  final int? id;
  final String? charId;
  final String? mediaType;
  final String? tag;
  final int? dur;
  final String? uri;
  final dynamic crtTm;
  final dynamic updTm;

  GenRes({
    this.id,
    this.charId,
    this.mediaType,
    this.tag,
    this.dur,
    this.uri,
    this.crtTm,
    this.updTm,
  });

  factory GenRes.fromRawJson(String str) => GenRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GenRes.fromJson(Map<String, dynamic> json) => GenRes(
        id: json["id"],
        charId: json["char_id"],
        mediaType: json["media_type"],
        tag: json["tag"],
        dur: json["dur"],
        uri: json["uri"],
        crtTm: json["crt_tm"],
        updTm: json["upd_tm"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "char_id": charId,
        "media_type": mediaType,
        "tag": tag,
        "dur": dur,
        "uri": uri,
        "crt_tm": crtTm,
        "upd_tm": updTm,
      };
}
