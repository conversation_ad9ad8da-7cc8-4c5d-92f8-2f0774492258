import 'dart:convert';

class MsgAnserLevel {
  final int? id;
  final String? userId;
  final int? conversationId;
  final String? charId;
  final int? level;
  final int? num;
  final double? progress;
  final double? upgradeRequirements;
  final int? rewards;

  MsgAnserLevel({
    this.id,
    this.userId,
    this.conversationId,
    this.charId,
    this.level,
    this.num,
    this.progress,
    this.upgradeRequirements,
    this.rewards,
  });

  MsgAnserLevel copyWith({
    int? id,
    String? userId,
    int? conversationId,
    String? charId,
    int? level,
    int? num,
    double? progress,
    double? upgradeRequirements,
    int? rewards,
  }) =>
      MsgAnserLevel(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        conversationId: conversationId ?? this.conversationId,
        charId: charId ?? this.charId,
        level: level ?? this.level,
        num: num ?? this.num,
        progress: progress ?? this.progress,
        upgradeRequirements: upgradeRequirements ?? this.upgradeRequirements,
        rewards: rewards ?? this.rewards,
      );

  factory MsgAnserLevel.fromRawJson(String str) => MsgAnserLevel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MsgAnserLevel.fromJson(Map<String, dynamic> json) => MsgAnserLevel(
        id: json['id'],
        userId: json['user_id'],
        conversationId: json['conversation_id'],
        charId: json['char_id'],
        level: json['level'],
        num: json['num'],
        progress: json['progress'],
        upgradeRequirements: json['upgrade_requirements'],
        rewards: json['rewards'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'user_id': userId,
        'conversation_id': conversationId,
        'char_id': charId,
        'level': level,
        'num': num,
        'progress': progress,
        'upgrade_requirements': upgradeRequirements,
        'rewards': rewards,
      };
}
