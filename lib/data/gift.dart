import 'dart:convert';

class Gift {
  final int? id;
  final String? perkName;
  final int? perkType;
  final String? img;
  final String? gdesc;
  final int? prc;

  Gift({
    this.id,
    this.perkName,
    this.perkType,
    this.img,
    this.gdesc,
    this.prc,
  });

  factory Gift.fromRawJson(String str) => Gift.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Gift.fromJson(Map<String, dynamic> json) => Gift(
        id: json["id"],
        perkName: json["perk_name"],
        perkType: json["perk_type"],
        img: json["img"],
        gdesc: json["gdesc"],
        prc: json["prc"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "perk_name": perkName,
        "perk_type": perkType,
        "img": img,
        "gdesc": gdesc,
        "prc": prc,
      };
}
