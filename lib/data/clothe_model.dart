import 'dart:convert';

class ClotheModel {
  final int? id;
  final int? clothingType;
  final DateTime? crtTm;
  final String? imageUrl;
  final dynamic imgRemark;
  final DateTime? updTm;
  final String? modelId;

  ClotheModel({
    this.id,
    this.clothingType,
    this.crtTm,
    this.imageUrl,
    this.imgRemark,
    this.updTm,
    this.modelId,
  });

  factory ClotheModel.fromRawJson(String str) => ClotheModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ClotheModel.fromJson(Map<String, dynamic> json) => ClotheModel(
        id: json["id"],
        clothingType: json["clothing_type"],
        crtTm: json["crt_tm"] == null ? null : DateTime.parse(json["crt_tm"]),
        imageUrl: json["image_url"],
        imgRemark: json["img_remark"],
        updTm: json["upd_tm"] == null ? null : DateTime.parse(json["upd_tm"]),
        modelId: json["model_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "clothing_type": clothingType,
        "crt_tm": crtTm?.toIso8601String(),
        "image_url": imageUrl,
        "img_remark": imgRemark,
        "upd_tm": updTm?.toIso8601String(),
        "model_id": modelId,
      };
}
