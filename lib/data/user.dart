import 'dart:convert';

class User {
  final String? id;
  final int? crtTm;
  final int? modifyTime;
  final String? devId;
  final String? tkn;
  final String? plt;
  final int? gms;
  final bool? audioSwitch;
  final dynamic subscriptionEnd;
  String? nickname;
  final String? gndr;
  final dynamic advid;
  final dynamic advId;
  final dynamic androidId;
  final dynamic gpsAdid;
  final bool? autoTranslate;
  final String? sourceLanguage;
  final String? targetLanguage;
  final String? platformName;
  final bool? vipLvl;

  User({
    this.id,
    this.crtTm,
    this.modifyTime,
    this.devId,
    this.tkn,
    this.plt,
    this.gms,
    this.audioSwitch,
    this.subscriptionEnd,
    this.nickname,
    this.gndr,
    this.advid,
    this.advId,
    this.androidId,
    this.gpsAdid,
    this.autoTranslate,
    this.sourceLanguage,
    this.targetLanguage,
    this.platformName,
    this.vipLvl,
  });

  factory User.fromRawJson(String str) => User.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        crtTm: json["crt_tm"],
        modifyTime: json["modify_time"],
        devId: json["dev_id"],
        tkn: json["tkn"],
        plt: json["plt"],
        gms: json["gms"],
        audioSwitch: json["audio_switch"],
        subscriptionEnd: json["subscription_end"],
        nickname: json["nickname"],
        gndr: json["gndr"],
        advid: json["advid"],
        advId: json["adv_id"],
        androidId: json["android_id"],
        gpsAdid: json["gps_adid"],
        autoTranslate: json["auto_translate"],
        sourceLanguage: json["source_language"],
        targetLanguage: json["target_language"],
        platformName: json["platform_name"],
        vipLvl: json["vip_lvl"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "crt_tm": crtTm,
        "modify_time": modifyTime,
        "dev_id": devId,
        "tkn": tkn,
        "plt": plt,
        "gms": gms,
        "audio_switch": audioSwitch,
        "subscription_end": subscriptionEnd,
        "nickname": nickname,
        "gndr": gndr,
        "advid": advid,
        "adv_id": advId,
        "android_id": androidId,
        "gps_adid": gpsAdid,
        "auto_translate": autoTranslate,
        "source_language": sourceLanguage,
        "target_language": targetLanguage,
        "platform_name": platformName,
        "vip_lvl": vipLvl,
      };
}
