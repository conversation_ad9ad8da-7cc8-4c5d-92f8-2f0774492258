import "dart:convert";

class UndrRes {
  final bool? success;
  final int? code;
  final String? message;
  final List<Datum>? data;

  UndrRes({
    this.success,
    this.code,
    this.message,
    this.data,
  });

  factory UndrRes.fromRawJson(String str) => UndrRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UndrRes.fromJson(Map<String, dynamic> json) => UndrRes(
        success: json["success"],
        code: json["code"],
        message: json["msg_body"],
        data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg_body": message,
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  final String? name;
  final String? style;
  final String? url;
  final int? price;

  Datum({
    this.name,
    this.style,
    this.url,
    this.price,
  });

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        name: json["nm"],
        style: json["style"],
        url: json["uri"],
        price: json["prc"],
      );

  Map<String, dynamic> toJson() => {
        "nm": name,
        "style": style,
        "uri": url,
        "prc": price,
      };
}
