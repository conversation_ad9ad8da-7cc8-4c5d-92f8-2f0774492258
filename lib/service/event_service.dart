import 'dart:async';

import 'package:bushy/data/event_data.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

class EventService {
  static final EventService _instance = EventService._internal();
  factory EventService() => _instance;
  EventService._internal();

  static Box<EventData>? _box;
  static const String boxName = 'ad_logs';

  Future<Box<EventData>> get box async {
    if (_box != null) return _box!;
    _box = await _initBox();
    return _box!;
  }

  Future<Box<EventData>> _initBox() async {
    final appDocumentDir = await path_provider.getApplicationDocumentsDirectory();
    Hive.init(appDocumentDir.path);
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(EventDataAdapter());
    }
    return await Hive.openBox<EventData>(boxName);
  }

  Future<void> insertLog(EventData log) async {
    final box = await this.box;
    return await box.put(log.id, log);
  }

  Future<List<EventData>> getUnuploadedLogs({int limit = 10}) async {
    final box = await this.box;
    return box.values.where((log) => !log.isUploaded).take(limit).toList();
  }

  Future<List<EventData>> getFailedLogs({int limit = 10}) async {
    final box = await this.box;
    return box.values.where((log) => !log.isSuccess).take(limit).toList();
  }

  Future<void> markLogsAsSuccess(List<EventData> logs) async {
    final box = await this.box;
    final now = DateTime.now().millisecondsSinceEpoch;
    try {
      for (final log in logs) {
        final updatedLog = EventData(
          id: log.id,
          eventType: log.eventType,
          data: log.data,
          isSuccess: true,
          createTime: log.createTime,
          uploadTime: now,
          isUploaded: true,
        );
        await box.put(log.id, updatedLog);
      }
    } catch (e) {
      throw Exception('Failed to update logs: $e');
    }
  }
}
