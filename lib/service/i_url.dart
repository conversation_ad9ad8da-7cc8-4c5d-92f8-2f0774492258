class IUrl {
  IUrl._();

  // 注册
  static const String register = '/snapshot/v1/user/device/reg';
  // 获取用户信息
  static const String getUserInfo = '/snapshot/v2/usr/getByDeviceId';
  // 修改用户信息
  static const String updateUserInfo = '/snapshot/v2/usr/update/user';
  // 角色列表
  static const String roleList = '/snapshot/v2/characterLang/getAll';
  // moments list
  static const String momentsList = '/snapshot/moments/lang/getAll';
  // 根据角色 id 查询角色
  static const String getRoleById = '/snapshot/v2/characterLang/getById';
  // 用户减钻石
  static const String minusGems = '/snapshot/v2/usr/minusGems';
  // 通过角色随机查一条查询
  static const String genRandomOne = '/snapshot/v2/characterMedia/getByRole/randomOne';
  // 支持 auto-mask 支持角色生成
  static const String undrCharacter = '/snapshot/nkd/udsRslt';
  // undr image result
  static const String undrImageRes = '/snapshot/nkd/getudsRslt';
  // undr styles
  static const String undrStyles = '/snapshot/nkd/getStyleConfig';
  // ios 创建订单
  static const String createIosOrder = '/snapshot/rcOrd/createOrder';
  // iOS 完成订单
  static const String verifyIosReceipt = '/snapshot/rcOrd/finishOrder';
  // 创建 google 订单
  static const String createAndOrder = '/snapshot/pay/google/create';
  // 谷歌验签
  static const String verifyAndOrder = '/snapshot/pay/google/verify';
  // 收藏角色
  static const String collectRole = '/snapshot/v2/chrPrf/collect';
  // 取消收藏角色
  static const String cancelCollectRole = '/snapshot/v2/chrPrf/cancelCollect';
  // 角色标签
  static const String roleTag = '/snapshot//v2/characterLang/tags';
  // 会话列表
  static const String sessionList = '/snapshot/aiConv/list';
  // 新增会话
  static const String addSession = '/snapshot/aiConv/add';
  // 重置会话
  static const String resetSession = '/snapshot/aiConv/reset';
  // 删除会话
  static const String deleteSession = '/snapshot/aiConv/delete';
  // 收藏列表
  static const String collectList = '/snapshot/v2/characterLang/collect/list';
  // 消息列表
  static const String messageList = '/snapshot/v2/history/getAll';
  // 语音聊天
  static const String voiceChat = '/snapshot/vox/chat';
  // 开屏随机角色
  static const String splashRandomRole = '/snapshot/pltCfg/recRole';
  // 上报事件 用户参数
  static String eventParams = '/v2/user/upinfo';
  // 聊天等级配置
  static String chatLevelConfig = '/system/chatLevelConf';
  // 解锁图片
  static String unlockImage = '/snapshot/v2/chrPrf/unlockImage';
  // 聊天等级
  static String chatLevel = '/snapshot/aiConv/getChatLevel';
  // translate
  static String translate = '/translate2';
  // 签到
  static String signIn = '/snapshot/signin';
  // 换装配置
  static String changeConfig = '/snapshot/v2/characterLang/getClothingConf';
  // 获取送礼配置
  static String getGiftConfig = '/snapshot/v2/characterLang/getPerk';
  // 送衣服
  static String sendClothes = '/snapshot/v2/msg/kits';
  // 送礼物
  static String sendGift = '/snapshot/v2/msg/perk';
  // 保存翻译结果消息
  static String saveTranslateMsg = '/snapshot/v2/history/saveMessage';
  // 事件上报
  static String event = "/eventLog/add";
  // sku 列表
  static String productList = '/platformConfig/getAllSku';
}
