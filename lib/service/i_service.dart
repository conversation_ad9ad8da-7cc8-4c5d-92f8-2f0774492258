import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/main.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../other/i_cache.dart';
import '../other/i_config.dart';
import '../other/info_helper.dart';
import '../other/log_util.dart';

class IService {
  // 单例模式
  static final IService _instance = IService._internal();
  factory IService() => _instance;
  IService._internal();

  final GetConnect _client = GetConnect();

  void init({required String baseUrl}) {
    _client.baseUrl = baseUrl;
    _client.timeout = const Duration(seconds: 60);
    // 配置 header
    _client.httpClient.addRequestModifier<dynamic>((request) async {
      final deviceId = await ICache().gDID();
      final version = await InfoHeper().version();
      var lang = locale.languageCode;

      if (lang == 'zh') {
        lang = 'zh-TW';
      }

      if (lang == 'pt') {
        lang = locale.toLanguageTag();
      }

      final headers = {
        'Content-Type': 'application/json',
        'device-id': deviceId,
        'platform': IConfig().platform,
        'version': version,
        'lang': lang
      };
      request.headers.addAll(headers);
      return request;
    });
  }

  /// GET 请求
  Future<Response> get(
    String path, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _client.get(
        path,
        headers: headers,
        query: queryParameters,
      );
      log.d('GET: ${_client.baseUrl} $path: ${response.request?.headers} $queryParameters');
      log.d('response: ${response.bodyString}\n');
      if (!response.isOk) {
        SmartDialog.showToast(LocaleKeys.no_network_connection.tr);
      }
      return response;
    } catch (e) {
      log.e('GET: ${_client.baseUrl} $path: catch  ${e.toString()}');
      return Response(statusCode: -1, statusText: 'Error: $e');
    }
  }

  /// POST 请求
  Future<Response> post(
    String path, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    dynamic body,
  }) async {
    try {
      final response = await _client.post(
        path,
        body,
        headers: headers,
        query: queryParameters,
      );
      log.i(
          'POST: ${_client.baseUrl} $path:${response.request?.headers} $queryParameters: $queryParameters body:$body');
      log.i('response: ${response.bodyString}\n');
      if (!response.isOk) {
        SmartDialog.showToast(LocaleKeys.no_network_connection.tr);
      }
      return response;
    } catch (e) {
      log.e('POST: ${_client.baseUrl} $path: catch ${e.toString()}');
      return Response(statusCode: -1, statusText: 'Error: $e');
    }
  }
}
