import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:adjust_sdk/adjust.dart';
import 'package:bushy/data/event_data.dart';
import 'package:bushy/main.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_config.dart';
import 'package:bushy/other/info_helper.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/service/event_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:uuid/v4.dart';

class EventApi {
  static final EventApi _instance = EventApi._internal();

  factory EventApi() => _instance;

  EventApi._internal() {
    _startUploadTimer();
    _startRetryTimer();
  }

  final _eventService = EventService();
  Timer? _uploadTimer;
  Timer? _retryTimer;

  void _startUploadTimer() {
    _uploadTimer?.cancel();
    _uploadTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _uploadPendingLogs();
    });
  }

  void _startRetryTimer() {
    _retryTimer?.cancel();
    _retryTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _retryFailedLogs();
    });
  }

  String get androidBurl => IConfig().isDebug
      ? "https://test-suppress.bushyai.com/variac/crewman/david"
      : "https://suppress.bushyai.com/stopover/collet";

  String get iosUrl => IConfig().isDebug
      ? "https://test-bogeyman.bushyai.com/exeter/anus"
      : "https://bogeyman.bushyai.com/kickback/dominate/bushnell";

  late final Dio _dio = Dio(
    BaseOptions(
      baseUrl: Platform.isAndroid ? androidBurl : iosUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ),
  );

  String uuid() {
    String uuid = const UuidV4().generate();
    return uuid;
  }

  // 获取通用参数
  Future<Map<String, dynamic>?> _getCommonParams() async {
    final deviceId = await ICache().gDID(o: true);
    final deviceModel = await InfoHeper().getDeviceModel();
    final manufacturer = await InfoHeper().getDeviceManufacturer();
    final idfv = await Adjust.getIdfv();
    final version = await InfoHeper().version();
    final osVersion = await InfoHeper().getOsVersion();
    final gaid = Platform.isAndroid ? await Adjust.getGoogleAdId() : null;

    if (Platform.isAndroid) {
      return {
        "ghoulish": {
          "dress": manufacturer,
          "swag": locale.toString(),
          "census": deviceId,
        },
        "ascend": {
          "chock": "mcc",
          "clerk": "lillian",
          "petit": version,
        },
        "paucity": {
          "toponym": idfv,
          "hydrous": DateTime.now().millisecondsSinceEpoch,
          "simonson": deviceModel,
        },
        "mangy": {
          "blank": deviceId,
          "monica": uuid(),
          "startle": version,
          "clammy": "gp",
          "opinion": version,
          "culpa": "com.blushai.meet",
          "figurate": gaid,
        },
      };
    }

    return {
      "linton": {
        "krill": version,
        "glisten": deviceId,
      },
      "patina": {
        "gage": manufacturer,
        "pasture": osVersion,
        "kickoff": uuid(),
        "homeobox": "com.bushyai.chat",
        "stokes": DateTime.now().millisecondsSinceEpoch,
        "process": deviceModel,
        "vivian": idfv,
      },
      "fuselage": {
        "cutoff": "",
        "peach": locale.toString(),
      },
      "drainage": {
        "brahms": manufacturer,
        "niobium": "bound",
        "mercy": "mcc",
      },
    };
  }

  Future<void> logInstallEvent() async {
    try {
      var data = await _getCommonParams() ?? {};

      final build = await InfoHeper().buildNumber();
      final isLimitAdTrackingEnabled = await InfoHeper().isLimitAdTrackingEnabled();
      final agent =
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36";

      if (Platform.isAndroid) {
        final pour = isLimitAdTrackingEnabled ? 'plumbate' : 'toothy';

        data["menial"] = "fungible";
        data["marque"] = "build.$build";
        data["baleen"] = agent;
        data["once"] = pour;
        data["somber"] = DateTime.now().millisecondsSinceEpoch;
        data["dye"] = DateTime.now().millisecondsSinceEpoch;
        data["instant"] = DateTime.now().millisecondsSinceEpoch;
        data["skin"] = DateTime.now().millisecondsSinceEpoch;
        data["polopony"] = DateTime.now().millisecondsSinceEpoch;
        data["snafu"] = DateTime.now().millisecondsSinceEpoch;
      } else {
        data["wry"] = "befallen";
        final pour = isLimitAdTrackingEnabled ? 'toil' : 'adrienne';
        data["marathon"] = "build.$build";
        data["galloway"] = agent;
        data["gossamer"] = pour;
        data["thisll"] = DateTime.now().millisecondsSinceEpoch;
        data["floppy"] = DateTime.now().millisecondsSinceEpoch;
        data["hale"] = DateTime.now().millisecondsSinceEpoch;
        data["cynthia"] = DateTime.now().millisecondsSinceEpoch;
        data["boreal"] = DateTime.now().millisecondsSinceEpoch;
        data["blouse"] = DateTime.now().millisecondsSinceEpoch;
      }

      final logModel = EventData(
        eventType: 'install',
        data: jsonEncode(data),
        createTime: DateTime.now().millisecondsSinceEpoch,
        id: uuid(),
      );
      await _eventService.insertLog(logModel);
      log.d('[event] InstallEvent saved to database');
    } catch (e) {
      log.e('[event] logEvent error: $e');
    }
  }

  Future<void> logSessionEvent() async {
    try {
      var data = await _getCommonParams();

      if (data == null) {
        return;
      }

      if (Platform.isAndroid) {
        data['menial'] = "slate";
      } else {
        data['wry'] = "arrive";
      }

      final logModel = EventData(
        id: data.logId,
        eventType: 'session',
        data: jsonEncode(data),
        createTime: DateTime.now().millisecondsSinceEpoch,
      );
      await _eventService.insertLog(logModel);
      log.d('[event] logSessionEvent saved to database');
    } catch (e) {
      log.e('logEvent error: $e');
    }
  }

  // Future<void> logAdEvent({
  //   required String adid,
  //   required String placement,
  //   required String adType,
  //   double? value,
  //   String? currency,
  // }) async {
  //   try {
  //     var data = await _getCommonParams();
  //     if (data == null) {
  //       return;
  //     }
  //     if (Platform.isAndroid) {
  //       data["higgins"] = {
  //         "luminous": "admob",
  //         "durkee": "admob",
  //         "bonnet": adid,
  //         "crowley": placement,
  //         "aback": adType,
  //       };
  //     } else {
  //       data['briny'] = {
  //         "cry": value?.toInt() ?? 0,
  //         "dragon": currency,
  //         "varistor": "admob",
  //         "acolyte": "admob",
  //         "pickle": adid,
  //         "drawl": placement,
  //         "dupe": adType,
  //       };
  //     }
  //     final logModel = AdLogModel(
  //       eventType: 'ad',
  //       data: jsonEncode(data),
  //       createTime: DateTime.now().millisecondsSinceEpoch,
  //       id: data.logId,
  //     );
  //     await _adLogService.insertLog(logModel);
  //     log.d('[event] logAdEvent saved to database');
  //   } catch (e) {
  //     log.e('[event] logEvent error: $e');
  //   }
  // }

  Future<void> logCustomEvent({
    required String name,
    required Map<String, Object>? params,
  }) async {
    try {
      var data = await _getCommonParams();
      if (data == null) {
        return;
      }
      if (Platform.isAndroid) {
        data['menial'] = name;
        data[name] = params;
      } else if (Platform.isIOS) {
        data['wry'] = name;
        data[name] = params;
      }

      final logModel = EventData(
        eventType: 'custom',
        data: jsonEncode(data),
        createTime: DateTime.now().millisecondsSinceEpoch,
        id: data.logId,
      );
      await _eventService.insertLog(logModel);
      log.d('[event] logCustomEvent saved to database');
    } catch (e) {
      log.e('[event] logCustomEvent error: $e');
    }
  }

  Future<void> _uploadPendingLogs() async {
    try {
      final logs = await _eventService.getUnuploadedLogs();
      if (logs.isEmpty) return;

      final List<dynamic> dataList = logs.map((log) => jsonDecode(log.data)).toList();

      // 打印 url
      // log.d('[event] logCustomEvent url: ${_dio.options.baseUrl}');
      // // 打印 datalist json 字符串
      // log.d('[event] logCustomEvent dataList: ${jsonEncode(dataList)}');

      final res = await _dio.post('', data: dataList);

      if (res.statusCode == 200) {
        await _eventService.markLogsAsSuccess(logs);
        log.d('[event] Batch upload success: ${logs.length} logs');
      } else {
        log.e('[event] Batch upload error: ${res.statusMessage}');
      }
    } catch (e) {
      log.e('[event] Batch upload catch: $e');
    }
  }

  Future<void> _retryFailedLogs() async {
    try {
      final failedLogs = await _eventService.getFailedLogs();
      if (failedLogs.isEmpty) return;

      final List<dynamic> dataList = failedLogs.map((log) => jsonDecode(log.data)).toList();
      final res = await _dio.post('', data: dataList);

      if (res.statusCode == 200) {
        await _eventService.markLogsAsSuccess(failedLogs);
        log.d('[event] Retry success for: ${failedLogs.length}');
      } else {
        final ids = failedLogs.map((e) => e.id).toList();
        log.e('[event] Retry failed for: $ids');
      }
    } catch (e) {
      log.e('[event] Retry failed catch: $e');
    }
  }
}

extension Clannish on Map<String, dynamic> {
  dynamic get logId => Platform.isAndroid ? this['mangy']['monica'] : this["patina"]["kickoff"];
}

class EventPage extends StatefulWidget {
  const EventPage({super.key});

  @override
  State<EventPage> createState() => _EventPageState();
}

class _EventPageState extends State<EventPage> {
  final _adLogService = EventService();
  List<EventData> _logs = [];
  bool _isLoading = true;
  String _filterType = 'all'; // all, pending, failed

  @override
  void initState() {
    super.initState();
    _loadLogs();
  }

  Future<void> _loadLogs() async {
    setState(() => _isLoading = true);
    try {
      final box = await _adLogService.box;
      var logs = box.values.toList();

      // Apply filter
      switch (_filterType) {
        case 'pending':
          logs = logs.where((log) => !log.isUploaded).toList();
          break;
        case 'failed':
          logs = logs.where((log) => !log.isSuccess).toList();
          break;
      }

      // Sort by createTime descending
      logs.sort((a, b) => b.createTime.compareTo(a.createTime));

      setState(() {
        _logs = logs;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      Get.snackbar('Error', 'Failed to load logs');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ad Logs'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() => _filterType = value);
              _loadLogs();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'all', child: Text('All Logs')),
              const PopupMenuItem(value: 'pending', child: Text('Pending Logs')),
              const PopupMenuItem(value: 'failed', child: Text('Failed Logs')),
            ],
            child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Icon(Icons.filter_list),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _logs.isEmpty
              ? const Center(child: Text('No logs found'))
              : RefreshIndicator(
                  onRefresh: _loadLogs,
                  color: Colors.blue,
                  child: ListView.builder(
                    itemCount: _logs.length,
                    itemBuilder: (context, index) {
                      final log = _logs[index];

                      var name = '';
                      try {
                        var dic = jsonDecode(log.data);
                        name = dic["wow"];
                      } catch (e) {}

                      return ListTile(
                        title: Text('Event: ${log.eventType}'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'id: ${log.id}',
                              style: const TextStyle(color: Colors.blue),
                            ),
                            if (name.isNotEmpty)
                              Text(
                                'name: $name',
                                style: const TextStyle(color: Colors.blue),
                              ),
                            Text('Created: ${DateTime.fromMillisecondsSinceEpoch(log.createTime)}'),
                            if (log.uploadTime != null)
                              Text('Uploaded: ${DateTime.fromMillisecondsSinceEpoch(log.uploadTime!)}'),
                            Row(
                              children: [
                                Icon(
                                  log.isUploaded ? Icons.cloud_done : Icons.cloud_upload,
                                  color: log.isUploaded ? Colors.green : Colors.orange,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  log.isUploaded ? 'Uploaded' : 'Pending',
                                  style: TextStyle(
                                    color: log.isUploaded ? Colors.green : Colors.orange,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                if (log.isUploaded)
                                  Icon(
                                    log.isSuccess ? Icons.check_circle : Icons.error,
                                    color: log.isSuccess ? Colors.green : Colors.red,
                                    size: 16,
                                  ),
                                const SizedBox(width: 4),
                                if (log.isUploaded)
                                  Text(
                                    log.isSuccess ? 'Success' : 'Failed',
                                    style: TextStyle(
                                      color: log.isSuccess ? Colors.green : Colors.red,
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text('Log Details - ${log.eventType}'),
                              content: SingleChildScrollView(
                                child: SelectableText(log.data), // 替换为SelectableText
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('Close'),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.content_copy),
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(text: log.data));
                                    Get.snackbar('Copied', 'Log data copied to clipboard');
                                  },
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
    );
  }
}
