import 'dart:async';
import 'dart:io';

import 'package:adjust_sdk/adjust.dart';
import 'package:bushy/data/base_res.dart';
import 'package:bushy/data/clothing_data.dart';
import 'package:bushy/data/datum.dart';
import 'package:bushy/data/gen_res.dart';
import 'package:bushy/data/gift.dart';
import 'package:bushy/data/level_config.dart';
import 'package:bushy/data/moment.dart';
import 'package:bushy/data/msg.dart';
import 'package:bushy/data/msg_anser_level.dart';
import 'package:bushy/data/msg_answer.dart';
import 'package:bushy/data/order_and.dart';
import 'package:bushy/data/order_ios.dart';
import 'package:bushy/data/product_model.dart';
import 'package:bushy/data/role_tag.dart';
import 'package:bushy/data/session.dart';
import 'package:bushy/data/user.dart';
import 'package:bushy/other/email_tool.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_config.dart';
import 'package:bushy/other/info_helper.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:bushy/service/i_service.dart';
import 'package:bushy/service/i_url.dart';
import 'package:dio/dio.dart';
import 'package:fast_rsa/fast_rsa.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../data/chater.dart';

final api = IService();

class IP {
  IP._();

  static Map<String, dynamic> get _qp => ICache().isBusy ? {'v': 'C001'} : {};

  static void _showError({String? msg}) {
    SmartDialog.showToast(msg ?? 'Something went wrong. Please try again later.');
  }

  static Future<User?> register() async {
    try {
      final deviceId = await ICache().gDID();
      var res = await api.post(
        IUrl.register,
        body: {
          "device_id": deviceId,
          "platform": IConfig().platform,
        },
      );
      if (!res.isOk) {
        return null;
      }
      final user = User.fromJson(res.body);
      return user;
    } catch (e) {
      return null;
    }
  }

  static Future<User?> getUserInfo() async {
    try {
      final deviceId = await ICache().gDID();
      final res = await api.get(IUrl.getUserInfo, queryParameters: {'device_id': deviceId});
      if (!res.isOk) {
        return null;
      }
      final user = User.fromJson(res.body);
      return user;
    } catch (e) {
      return null;
    }
  }

  static Future<bool> updateUserInfo(Map<String, dynamic> body) async {
    try {
      final res = await api.post(IUrl.updateUserInfo, body: body);
      final result = BaseRes.fromJson(res.body, null);
      return result.data;
    } catch (e) {
      return false;
    }
  }

  static Future<List<RoleTagRes>?> roleTagsList() async {
    try {
      var res = await api.get(IUrl.roleTag, queryParameters: _qp);
      if (res.isOk) {
        if (res.body is List) {
          final list = (res.body as List).map((e) => RoleTagRes.fromJson(e)).toList();
          return list;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // 获取开屏随机角色
  static Future<Chater?> splashRandomRole() async {
    try {
      var res = await api.get(
        IUrl.splashRandomRole,
        queryParameters: _qp,
      );
      if (res.isOk) {
        var result = BaseRes.fromJson(res.body, (json) => Chater.fromJson(json));
        return result.data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<ChaterRes?> homeList({
    required int page,
    required int size,
    String? rendStyl,
    String? name,
    bool? videoChat,
    bool? genImg,
    bool? genVideo,
    bool? changeClothing,
    List<int>? tags,
  }) async {
    try {
      var data = {'page': page, 'size': size, 'platform': IConfig().platform};
      if (rendStyl != null) {
        data['render_style'] = rendStyl;
      }
      if (videoChat != null) {
        data['video_chat'] = videoChat;
      }
      if (genImg != null) {
        data['gen_img'] = genImg;
      }
      if (genVideo != null) {
        data['gen_video'] = genVideo;
      }
      if (changeClothing != null) {
        data['change_clothing'] = changeClothing;
      }
      if (name != null) {
        data['name'] = name;
      }
      if (tags != null && tags.isNotEmpty) {
        data['tags'] = tags;
      }
      var res = await api.post(IUrl.roleList, body: data, queryParameters: _qp);
      if (res.isOk) {
        return ChaterRes.fromJson(res.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<MomentsRes?> momensListPage({
    required int page,
    required int size,
  }) async {
    try {
      var res = await api.post(
        IUrl.momentsList,
        body: {
          'page': page,
          'size': size,
          'hide_character': ICache().isBusy ? true : false,
        },
        queryParameters: _qp,
      );
      if (res.isOk) {
        return MomentsRes.fromJson(res.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<Chater?> loadRoleById(String roleId) async {
    try {
      var qp = _qp;
      qp['id'] = roleId;
      var res = await api.get(
        IUrl.getRoleById,
        queryParameters: qp,
      );
      if (res.isOk) {
        var role = Chater.fromJson(res.body);
        return role;
      } else {
        return null;
      }
    } catch (e) {
      log.e(e.toString());
      return null;
    }
  }

  /// api signature msg
  static Future<String?> getApiSignature() async {
    try {
      final uid = UserHelper().user?.id;
      if (uid == null || uid.isEmpty) return null;
      // 充值公钥DER 格式的公钥 Base64 编码
      const derEncodedPublicKey =
          'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLWMEjJb703WZJ5Nqf7qJ2wefSSYvbmQZM0CgHGrYstUaj4Mlz+P06mCqpVAYmyf3dJxLrEsUiobWvhi1Ut5W+PY0yrzEsIOJ5lJrIt1pm0/kcPsPj2d4cEl9S7DTEIJVQTGMzquAlhEkgbA0yDVXNtqqf4MECCADU/WM3WTCH2QIDAQAB';
      // 使用公钥加密消息
      // 将 DER 格式转换为 PEM 格式
      const pemPublicKey = '${'-----BEGIN PUBLIC KEY-----\n$derEncodedPublicKey'}\n-----END PUBLIC KEY-----';
      // 使用 RSA 公钥加密消息
      final encryptedMessage = await RSA.encryptPKCS1v15(uid, pemPublicKey);
      return encryptedMessage;
    } catch (e) {
      return null;
    }
  }

  static Future<int> consumeReq(int value, String from) async {
    // 使用公钥加密消息
    final uid = UserHelper().user?.id;
    if (uid == null || uid.isEmpty) return 0;
    final signature = await getApiSignature();

    var body = <String, dynamic>{
      'signature': signature,
      'id': uid,
      'gems': value,
      'description': from,
    };

    try {
      var res = await api.post(
        IUrl.minusGems,
        body: body,
        queryParameters: _qp,
      );
      if (res.isOk) {
        return res.body;
      } else {
        return 0;
      }
    } catch (e) {
      return 0;
    }
  }

  static Future<GenRes?> genResult({
    required String id,
    required String mediaType,
    required String tag,
  }) async {
    var queryParameters = <String, dynamic>{};
    queryParameters['media_type'] = mediaType.toUpperCase();
    queryParameters['tag'] = tag;
    queryParameters['id'] = id;

    try {
      var res = await api.get(
        IUrl.genRandomOne,
        queryParameters: queryParameters,
      );
      if (res.isOk) {
        var data = GenRes.fromJson(res.body);
        return data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<String?> undrChater(String id, String? style) async {
    try {
      var res = await api.post(
        IUrl.undrCharacter,
        body: {'character_id': id, 'style': style},
        queryParameters: _qp,
      );
      if (res.isOk) {
        var result = BaseRes.fromJson(res.body, null);
        if (result.code == 20003) {
          SmartDialog.showToast('Gems are not enough, please recharge');
        }

        return result.data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<String?> undrImage({
    required String sourceImage,
    required String fileName,
    required String mask,
    required String style,
  }) async {
    try {
      final deviceId = await ICache().gDID();
      final version = await InfoHeper().version();
      final headers = {
        'Content-Type': 'application/json',
        'device-id': deviceId,
        'platform': IConfig().platform,
        'version': version,
      };

      var dio = Dio(BaseOptions(
        baseUrl: IConfig().baseUrl,
        headers: headers,
        sendTimeout: const Duration(minutes: 1),
        receiveTimeout: const Duration(minutes: 3),
      ));

      dio.interceptors.add(LogInterceptor(responseBody: true));

      var res = await dio.request(
        IUrl.undrImageRes,
        data: {
          'source_image': sourceImage,
          'file_name': fileName,
          'mask': mask,
          'style': style,
        },
        options: Options(
          method: 'POST',
        ),
        queryParameters: _qp,
      );
      if (res.statusCode == 200) {
        var result = BaseRes<String>.fromJson(res.data, null);
        if (result.code == 20003) {
          SmartDialog.showToast('Gems are not enough, please recharge');
        }
        return result.data;
      } else {
        log.e('undrImage: ${res.statusCode}');
        return null;
      }
    } catch (e) {
      log.e('undrImage: ${e.toString()}');
      if (e is TimeoutException) {
        log.e('undrImage: Request timed out');
      } else {
        log.e('undrImage: ${e.toString()}');
      }
      return null;
    }
  }

  static Future<List<Datum>> getUndrStyles() async {
    try {
      final res = await api.get(IUrl.undrStyles, queryParameters: _qp);
      if (res.isOk) {
        var r = UndrRes.fromJson(res.body);
        return r.data ?? [];
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  static Future<OrderIosRes?> makeIosOrder({
    required String skuId,
    required String orderType,
  }) async {
    try {
      final userId = UserHelper().user?.id;
      if (userId == null || userId.isEmpty) return null;

      final deviceId = await ICache().gDID();

      var body = {
        'user_id': userId,
        'sku_id': skuId,
        'order_type': orderType,
        'device_id': deviceId,
      };

      var res = await api.post(IUrl.createIosOrder, body: body);
      if (res.isOk) {
        final result = BaseRes.fromJson(res.body, (data) => OrderIosRes.fromJson(data));
        return result.data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<bool> verifyIosOrder({
    required int orderId,
    required String receipt,
    required String skuId,
    required String transactionId,
    required String purchaseDate,
  }) async {
    try {
      final userId = UserHelper().user?.id;
      if (userId == null || userId.isEmpty) return false;

      var chooseEnv = IConfig().isDebug ? false : true;
      final idfa = await Adjust.getIdfa();
      final adid = await Adjust.getAdid();

      var params = <String, dynamic>{
        'order_id': orderId,
        'user_id': userId,
        'receipt': receipt,
        'choose_env': chooseEnv,
        'idfa': idfa,
        'adid': adid,
        'sku_id': skuId,
        'transaction_id': transactionId,
        'purchase_date': purchaseDate,
      };

      var res = await api.post(IUrl.verifyIosReceipt, body: params);
      if (res.isOk) {
        var data = BaseRes.fromJson(res.body, null);
        if (data.code == 0 || data.code == 200) {
          return true;
        }
        return false;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  static Future<OrderAndRes?> makeAndOrder({
    required String orderType,
    required String skuId,
  }) async {
    try {
      final userId = UserHelper().user?.id;
      if (userId == null || userId.isEmpty) return null;

      final deviceId = await ICache().gDID();

      var body = {
        'device_id': deviceId,
        'platform': IConfig().platform,
        'order_type': orderType,
        'sku_id': skuId,
        'user_id': userId,
      };

      var res = await api.post(IUrl.createAndOrder, body: body);

      if (res.isOk) {
        var result = BaseRes.fromJson(res.body, (data) => OrderAndRes.fromJson(data));
        return result.data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // 安卓验签
  static Future<bool> verifyAndOrder({
    required String originalJson,
    required String purchaseToken,
    required String orderType,
    required String skuId,
    required String orderId,
  }) async {
    try {
      final userId = UserHelper().user?.id;
      if (userId == null || userId.isEmpty) return false;
      String androidId = await ICache().gDID(o: true);
      final adid = await Adjust.getAdid();
      final gpsAdid = await Adjust.getGoogleAdId();

      var body = {
        'original_json': originalJson,
        'purchase_token': purchaseToken,
        'order_type': orderType,
        'sku_id': skuId,
        'order_id': orderId,
        'android_id': androidId,
        'gps_adid': gpsAdid,
        'adid': adid,
        'user_id': userId,
      };

      var res = await api.post(IUrl.verifyAndOrder, body: body);

      if (res.isOk) {
        final data = BaseRes.fromJson(res.body, null);
        if (data.code == 0 || data.code == 200) {
          return true;
        }
        return false;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  static Future<bool> collectRole(String roleId) async {
    try {
      var res = await api.post(IUrl.collectRole, body: {'character_id': roleId});
      return res.isOk;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> cancelCollectRole(String roleId) async {
    try {
      var res = await api.post(IUrl.cancelCollectRole, body: {'character_id': roleId});
      return res.isOk;
    } catch (e) {
      return false;
    }
  }

  static Future<SessionRes?> sessionList(int page, int size) async {
    try {
      var res = await api.post(
        IUrl.sessionList,
        body: {'page': page, 'size': size},
        queryParameters: _qp,
      );
      if (res.isOk) {
        return SessionRes.fromJson(res.body);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<Session?> addSession(String charId) async {
    try {
      var res = await api.post(
        IUrl.addSession,
        queryParameters: {'charId': charId},
      );
      if (res.isOk) {
        return Session.fromJson(res.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<Session?> resetSession(int id) async {
    try {
      var res = await api.post(
        IUrl.resetSession,
        queryParameters: {'conversationId': id.toString()},
      );
      if (res.isOk) {
        return Session.fromJson(res.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<bool> deleteSession(int id) async {
    try {
      var res = await api.post(
        IUrl.deleteSession,
        queryParameters: {'id': id.toString()},
      );
      return res.isOk;
    } catch (e) {
      return false;
    }
  }

  static Future<ChaterRes?> collectList(int page, int size) async {
    try {
      var res = await api.post(
        IUrl.collectList,
        body: {'page': page, 'size': size},
        queryParameters: _qp,
      );
      if (res.isOk) {
        var data = ChaterRes.fromJson(res.body);
        return data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // 消息列表
  static Future<MsgRes?> messageList(int page, int size, int convId) async {
    try {
      var res = await api.post(
        IUrl.messageList,
        body: {'page': page, 'size': size, 'conversation_id': convId},
        queryParameters: _qp,
      );
      if (res.isOk) {
        var data = MsgRes.fromJson(res.body);
        return data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<MsgAnswer?> sendVoiceChatMsg({
    required String roleId,
    required String userId,
    required String nickName,
    required String message,
    String? msgId,
  }) async {
    try {
      var res = await api.post(
        IUrl.voiceChat,
        body: {
          'char_id': roleId,
          'user_id': userId,
          'nick_name': nickName,
          'message': message,
          if (msgId?.isNotEmpty == true) 'msg_id': msgId,
        },
        queryParameters: _qp,
      );
      if (res.isOk) {
        return MsgAnswer.fromJson(res.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future<bool> updateEventParams({bool? autoTranslate}) async {
    try {
      final deviceId = await ICache().gDID();
      final adid = await Adjust.getAdid();

      Map<String, dynamic> data = {
        'adid': adid,
        'device_id': deviceId,
        'platform': IConfig().platform,
      };

      if (Platform.isIOS) {
        String? idfa = await Adjust.getIdfa();
        data['idfa'] = idfa;
      } else if (Platform.isAndroid) {
        final gpsAdid = await Adjust.getGoogleAdId();
        data['gps_adid'] = gpsAdid;
      }

      if (autoTranslate != null) {
        data['auto_translate'] = autoTranslate;
      }
      data['source_language'] = 'en';
      data['target_language'] = Get.deviceLocale?.languageCode;

      var result = await api.post(
        IUrl.eventParams,
        body: data,
      );

      final res = BaseRes.fromJson(result.body, null);
      if (res.code == 0 || res.code == 200) {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<List<LevelConfig>?> getChatLevelConfig() async {
    try {
      var result = await api.get(
        IUrl.chatLevelConfig,
      );
      final list = result.body;
      if (list is List) {
        final datas = list.map((x) => LevelConfig.fromJson(x)).toList();
        return datas;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<bool> unlockImageReq(int imageId, String modelId) async {
    try {
      var result = await api.post(
        IUrl.unlockImage,
        body: {
          'image_id': imageId,
          'model_id': modelId,
        },
      );

      return result.body;
    } catch (e) {
      return false;
    }
  }

  static Future<MsgAnserLevel?> fetchChatLevel({
    required String charId,
    required String userId,
  }) async {
    try {
      var qb = _qp;
      qb['charId'] = charId;
      qb['userId'] = userId;

      var result = await api.post(
        IUrl.chatLevel,
        queryParameters: qb,
      );
      if (result.isOk) {
        var res = BaseRes.fromJson(result.body, (json) => MsgAnserLevel.fromJson(json));
        return res.data;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<String?> translateText(String content, String to) async {
    final phoneLang = ICache().locale?.languageCode ?? '';
    _logTranslateEvent('translate_start', content, to, phoneLang);

    try {
      final emailResult = await EmailTool().translate(content, to: to);
      if (emailResult?.isNotEmpty == true) {
        _logTranslateEvent('translate_other', content, to, phoneLang);
        return emailResult;
      }

      var targetLanguage = to;
      if (to.contains('zh')) {
        targetLanguage = 'zh-TW';
      }

      final result = await api.post(
        IUrl.translate,
        body: {'content': content, 'target_language': targetLanguage},
      );

      final res = BaseRes.fromJson(result.body, null);
      if (res.data is String && res.data.toString().isNotEmpty) {
        _logTranslateEvent('translate_api', content, to, phoneLang);
        return res.data;
      }
    } catch (e) {
      // 可选加日志或上报错误
      debugPrint('Translation failed: $e\n');
      _logTranslateEvent('translate_error', content, to, phoneLang);
    }

    return null;
  }

  static void _logTranslateEvent(String event, String content, String to, String phoneLang) {
    try {
      IP.addEvent(event: event, params: {
        'text': content,
        'to': to,
        'phoneLang': phoneLang,
      });
    } catch (_) {}
  }

  static Future getDailyReward() async {
    try {
      var result = await api.post(
        IUrl.signIn,
      );
      final res = BaseRes.fromJson(result.body, null);
      if (res.code == 0 || res.code == 200) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 获取换装配置
  static Future<List<ClothingData>?> getChangeConfig() async {
    try {
      var result = await api.get(IUrl.changeConfig);
      if (result.body is List) {
        final list = (result.body as List).map((e) => ClothingData.fromJson(e)).toList();
        return list;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取送礼配置
  static Future<List<Gift>?> getGiftConfig() async {
    try {
      var result = await api.get(
        IUrl.getGiftConfig,
      );
      if (result.body is List) {
        final list = (result.body as List).map((e) => Gift.fromJson(e)).toList();
        return list;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 送衣服
  static Future<Msg?> sendClothes({required int convId, required int clothingId, required String roleId}) async {
    try {
      var result = await api.post(IUrl.sendClothes, body: {
        'conversation_id': convId,
        'id': clothingId,
        'model_id': roleId,
      });
      var res = BaseRes.fromJson(result.body, (json) => Msg.fromJson(json));
      if (res.code == 0 || res.code == 200) {
        return res.data;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 送礼物
  static Future<Msg?> sendGift({required int convId, required int giftId, required String roleId}) async {
    try {
      var result = await api.post(IUrl.sendGift, body: {
        'conversation_id': convId,
        'id': giftId,
        'model_id': roleId,
      });
      var res = BaseRes.fromJson(result.body, (json) => Msg.fromJson(json));
      if (res.code == 0 || res.code == 200) {
        return res.data;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 保存翻译结果消息
  static Future<bool> saveTranslateMsg({
    required String id,
    required String translateAnswer,
  }) async {
    try {
      var result = await api.post(
        IUrl.saveTranslateMsg,
        body: {
          'translate_answer': translateAnswer,
          'id': id,
        },
      );
      return result.isOk;
    } catch (e) {
      return false;
    }
  }

  /// 添加埋点事件
  static Future addEvent({required String event, Map<String, dynamic>? params}) async {
    try {
      api.post(IUrl.event, body: {'event_name': event, 'params': params});
    } catch (e) {
      return false;
    }
  }

  /// sku 列表
  static Future<List<ProductModel>?> fetchSku() async {
    try {
      var result = await api.get(IUrl.productList);
      var res = BaseRes.fromJson(result.body, null);
      if (res.data != null) {
        List<ProductModel> skus = [];
        for (var item in res.data) {
          skus.add(ProductModel.fromJson(item));
        }
        return skus;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
