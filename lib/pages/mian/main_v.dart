import 'package:bushy/common/gradient_painter.dart';
import 'package:bushy/other/email_tool.dart';
import 'package:bushy/other/fb_sdk_util.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/service/event_api.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

import '../../common/keep_alive_wrapper.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_cache.dart';
import '../../other/i_config.dart';
import '../../other/svg_icon.dart';
import '../chat/chat_page.dart';
import '../home/<USER>';
import '../home/<USER>';

RootIndex rootIndex = RootIndex.home;

enum RootIndex {
  home,
  moment,
  creat,
  me,
}

class MainV extends StatefulWidget {
  const MainV({super.key});

  @override
  State<MainV> createState() => _MainVState();
}

class _MainVState extends State<MainV> with WidgetsBindingObserver {
  final iconSize = 24.0;
  late final Future<void> _initFuture;
  late final List<Widget> _pages;
  late List<BottomNavigationBarItem> _items;

  @override
  void initState() {
    super.initState();
    _initFuture = _initialize();

    // 添加生命周期监听器
    WidgetsBinding.instance.addObserver(this);

    // 确保Facebook SDK初始化
    try {
      EmailTool().login();
      FBSDKUtil.initializeWithRemoteConfig();
    } catch (fbError) {
      log.e('Facebook SDK初始化失败: $fbError');
    }
  }

  @override
  void dispose() {
    // 移除生命周期监听器
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        log.d('App is foregrounded');
        // 应用进入前台时的处理逻辑
        EventApi().logSessionEvent();
        break;
      case AppLifecycleState.paused:
        log.d('App is backgrounded');
        // 应用进入后台时的处理逻辑
        break;
      case AppLifecycleState.inactive:
        log.d('App is inactive');
        // 应用处于非活跃状态
        break;
      case AppLifecycleState.detached:
        log.d('App is detached');
        // 应用被分离
        break;
      case AppLifecycleState.hidden:
        log.d('App is hidden');
        // 应用被隐藏
        break;
    }
  }

  Future<void> _initialize() async {
    try {
      await IConfig().requstBloom(count: 0);
    } catch (e) {
      throw Exception('初始化失败：${e.toString()}');
    }

    _initializePages();
    _initializeItems();
  }

  void _initializePages() {
    _pages = [
      const KeepAliveWrapper(child: HomePage()),
      const KeepAliveWrapper(child: ChatPage()),
      KeepAliveWrapper(child: MomentsV()),
      // if (ICache().isBusy) const KeepAliveWrapper(child: GenPage()),
    ];
  }

  void _initializeItems() {
    _items = [
      _buildNavigationItem(
        icon: Assets.images.newUi.bnbiHome,
        label: LocaleKeys.home.tr,
      ),
      _buildNavigationItem(
        icon: Assets.images.newUi.bnbiChat,
        label: LocaleKeys.chat.tr,
      ),
      _buildNavigationItem(
        icon: Assets.images.newUi.bnbiMoments,
        label: LocaleKeys.moments.tr,
        isRed: ICache().tmc == 0,
      ),
      // if (ICache().isBusy)
      //   _buildNavigationItem(
      //     icon: Assets.images.newUi.bnbiCreat,
      //     label: LocaleKeys.create.tr,
      //     isRed: ICache().tcc == 0,
      //   ),
    ];
  }

  BottomNavigationBarItem _buildNavigationItem({
    required String icon,
    required String label,
    bool isRed = false,
  }) {
    Widget buildIcon(bool active) {
      return Stack(
        alignment: AlignmentDirectional.topEnd,
        children: [
          SvgIcon(
            assetName: icon,
            width: iconSize,
            height: iconSize,
            color: active ? const Color(0xff906BF7) : const Color(0xffDEDEDE),
          ),
          if (isRed)
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xffE44341),
              ),
            )
        ],
      );
    }

    return BottomNavigationBarItem(
      icon: buildIcon(false),
      activeIcon: buildIcon(true),
      label: label,
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return _buildLoading();
        }

        _initializeItems();

        return Scaffold(
          body: IndexedStack(
            index: RootIndex.values.indexOf(rootIndex),
            children: _pages,
          ),
          bottomNavigationBar: Theme(
            data: ThemeData(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
            ),
            child: BottomNavigationBar(
              currentIndex: RootIndex.values.indexOf(rootIndex),
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.white,
              onTap: (index) {
                rootIndex = RootIndex.values[index];
                if (rootIndex == RootIndex.creat) {
                  ICache().tcc = 1;
                }
                if (rootIndex == RootIndex.moment) {
                  ICache().tmc = 1;
                }
                _initializeItems();
                setState(() {});
              },
              items: _items,
              selectedItemColor: const Color(0xff906BF7),
              unselectedItemColor: const Color(0xffDEDEDE),
              selectedLabelStyle: GoogleFonts.montserrat(
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: GoogleFonts.montserrat(
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoading() {
    return Container(
      color: Colors.white,
      child: Stack(
        children: [
          CustomPaint(
            size: Size(MediaQuery.sizeOf(context).width, 258.0),
            painter: GradientPainter(),
          ),
          Container(
            // color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: Shimmer.fromColors(
              baseColor: const Color(0xffDEDEDE),
              highlightColor: const Color(0xffF2F2F2),
              child: Column(
                spacing: 20,
                children: [
                  SafeArea(
                    bottom: false,
                    child: Row(
                      spacing: 8,
                      children: [
                        Container(
                          width: 78,
                          height: 24,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(12)),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          width: 24,
                          height: 24,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(12)),
                          ),
                        ),
                        Container(
                          width: 78,
                          height: 24,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(12)),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    spacing: 20,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(3, (_) {
                      return Container(
                        width: 109,
                        height: 109,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                        ),
                      );
                    }),
                  ),
                  Expanded(
                    child: GridView.builder(
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        childAspectRatio: 0.7,
                      ),
                      itemCount: 10, // 模拟加载的项目数量，这里设置为10个
                      itemBuilder: (BuildContext context, int index) {
                        // 列表项构建器，根据index创建每个列表项
                        return Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(12)),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
