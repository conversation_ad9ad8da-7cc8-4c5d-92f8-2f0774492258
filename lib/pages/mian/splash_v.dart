import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_indicator/loading_indicator.dart' as LD;

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_config.dart';
import '../../other/i_router.dart';
import '../../other/info_helper.dart';
import '../../other/log_util.dart';
import '../../other/net_obs.dart';
import '../../other/user_helper.dart';

class SplashV extends StatefulWidget {
  const SplashV({super.key});

  @override
  State<SplashV> createState() => _SplashVState();
}

class _SplashVState extends State<SplashV> {
  @override
  void initState() {
    super.initState();
    if (NetObs.to.isConnected.value) {
      setup();
    } else {
      ever(NetObs.to.isConnected, (v) {
        if (v) {
          setup();
        }
      });
    }
  }

  Future<void> setup() async {
    try {
      await InfoHeper().getIdfa();

      await Future.wait([
        IConfig().requstBloom(count: 0),
        UserHelper().register(),
        IConfig().fetchConfig(),
      ]).timeout(const Duration(seconds: 7));
    } catch (e) {
      // 捕获超时异常或其他异常
      log.e('Setup tasks timed out: $e');
    }

    // 即使超时，仍继续执行后续逻辑
    await UserHelper().getUserInfo();

    IRouter.pushRoot();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff906BF7),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Assets.images.newUi.spBg.image(),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Assets.images.newUi.spLogo.image(width: 120),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.effortless_teamwork.tr,
                    style: GoogleFonts.montserrat(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const SizedBox(
                    width: 44,
                    height: 44,
                    child: LD.LoadingIndicator(
                      indicatorType: LD.Indicator.pacman,
                      colors: [Colors.white],
                    ),
                  ),
                  const SizedBox(height: 44),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
