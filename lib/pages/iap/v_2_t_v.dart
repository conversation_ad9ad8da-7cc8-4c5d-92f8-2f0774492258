import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../common/gradient_text.dart';
import '../../generated/locales.g.dart';
import '../../other/i_theme.dart';

class V2TV extends StatefulWidget {
  const V2TV({super.key});

  @override
  State<V2TV> createState() => _V2TVState();
}

class _V2TVState extends State<V2TV> {
  int minutes = 30;
  int seconds = 0;
  Timer? _timer;

  @override
  void initState() {
    _startTimer();
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String minutesStr = minutes.toString().padLeft(2, '0');
    String secondsStr = seconds.toString().padLeft(2, '0');

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          LocaleKeys.expiration_time.tr,
          style: TextStyle(
            color: Colors.white,
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(width: 4.w),
        _buildDigit(minutesStr),
        SizedBox(width: 8.w),
        const GradientText(
          data: ":",
          gradient: LinearGradient(
            colors: IColor.brandjb,
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(width: 8.w),
        _buildDigit(secondsStr),
      ],
    );
  }

  Container _buildDigit(String digit) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: IColor.white1,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.25),
            blurRadius: 4,
          ),
        ],
      ),
      child: Center(
        child: GradientText(
          data: digit,
          gradient: const LinearGradient(
            colors: IColor.brandjb,
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          style: GoogleFonts.montserrat(
            fontSize: 11,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (seconds == 0) {
        if (minutes == 0) {
          timer.cancel();
        } else {
          minutes--;
          seconds = 59;
        }
      } else {
        seconds--;
      }

      if (mounted) {
        setState(() {});
      }
    });
  }
}
