import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../generated/locales.g.dart';
import '../../other/i_cache.dart';
import '../../other/i_theme.dart';

class VBtn extends StatelessWidget {
  const VBtn({super.key, this.onTap});

  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      margin: const EdgeInsets.symmetric(horizontal: 60),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(18)),
        color: IColor.brand,
      ),
      child: InkWell(
        borderRadius: const BorderRadius.all(Radius.circular(18)),
        onTap: onTap,
        child: Center(
          child: Text(
            ICache().isBusy ? LocaleKeys.subscribe.tr : LocaleKeys.buy.tr,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: ITStyle.l4sem(IColor.n10),
          ),
        ),
      ),
    );
  }
}
