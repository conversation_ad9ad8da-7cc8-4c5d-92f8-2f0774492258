import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../gen/assets.gen.dart';

class VR extends StatelessWidget {
  const VR({super.key, required this.strList, required this.emojis});

  final List<String> strList;
  final List<String> emojis;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (_, index) {
        return Row(
          children: [
            Assets.images.newUi.choose.image(width: 16, height: 16),
            const SizedBox(width: 4),
            Text(emojis[index], style: const TextStyle(fontSize: 20, height: 1.0)),
            const SizedBox(width: 4),
            Text(
              strList[index],
              style: GoogleFonts.montserrat(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      },
      separatorBuilder: (_, i) {
        return const SizedBox(height: 8);
      },
      itemCount: strList.length,
    );
  }
}
