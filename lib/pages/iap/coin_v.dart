import 'package:bushy/data/product_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/event_util.dart';
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import '../../other/i_theme.dart';
import '../../other/iap_util.dart';
import '../../other/info_helper.dart';
import 'coin_is.dart';
import 'policy_s.dart';
import 'q_a_list.dart';

class CoinV extends StatefulWidget {
  const CoinV({super.key});

  @override
  State<CoinV> createState() => _CoinVState();
}

class _CoinVState extends State<CoinV> {
  ProductModel? _chooseModel;

  late ConsumeSource from;

  List<ProductModel> list = [];

  @override
  void initState() {
    super.initState();

    InfoHeper().getIdfa();

    _loadData();

    if (Get.arguments != null && Get.arguments is ConsumeSource) {
      from = Get.arguments;
    }

    logEvent('t_paygems');
  }

  Future<void> _loadData() async {
    SmartDialog.showLoading();
    await IAPUtil().queryProducts();
    setState(() {});
    SmartDialog.dismiss();

    list = IAPUtil().consumableList;
    _chooseModel = list.firstWhereOrNull((e) => e.defaultSku == true);
    setState(() {});
  }

  void _showHelp() {
    SmartDialog.show(
      clickMaskDismiss: true,
      maskColor: IColor.black8,
      builder: (_) {
        return const Center(
          child: QAList(),
        );
      },
    );
  }

  void _buy() {
    if (_chooseModel != null) {
      logEvent('c_paygems');
      IAPUtil().buy(_chooseModel!, consFrom: from);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Center(
            child: Assets.images.newUi.navCloseBlack.image(width: 26),
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showHelp,
            icon: Center(
              child: Assets.images.newUi.navQa.image(width: 24),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 57),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: IColor.brandjb,
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: kToolbarHeight + MediaQuery.paddingOf(context).top,
                  ),
                  Text(
                    ICache().isBusy ? LocaleKeys.open_chats_unlock.tr : LocaleKeys.buy_gems_open_chats.tr,
                    style: ITStyle.l4reg(IColor.n10),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
            const SizedBox(height: 24),
            CoinIS(
              list: list,
              chooseProduct: _chooseModel,
              onTap: (product) {
                _chooseModel = product;
                setState(() {});
              },
            ),
            const SizedBox(height: 24),
            list.isEmpty
                ? const SizedBox()
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    child: Text(
                      LocaleKeys.one_time_purchase_note.trParams({
                        'price': _chooseModel?.productDetails?.price ?? '',
                      }),
                      textAlign: TextAlign.center,
                      style: ITStyle.l6reg(IColor.n03),
                    ),
                  ),
            const SizedBox(height: 16),
            _buildButton(),
            const SizedBox(height: 8),
            const Policys(type: PolicyBottomType.gems),
          ],
        ),
      ),
    );
  }

  Widget _buildButton() {
    if (list.isEmpty) {
      return const SizedBox();
    }
    return GestureDetector(
      onTap: _buy,
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        margin: const EdgeInsets.symmetric(horizontal: 60),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(16)),
          color: IColor.brand,
        ),
        child: Center(
          child: Text(
            ICache().isBusy ? LocaleKeys.btn_continue.tr : LocaleKeys.buy.tr,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: ITStyle.l4sem(IColor.n10),
          ),
        ),
      ),
    );
  }
}
