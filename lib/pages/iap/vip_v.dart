import 'package:bushy/data/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/event_util.dart';
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import '../../other/i_theme.dart';
import '../../other/iap_util.dart';
import '../../other/info_helper.dart';
import 'v_1_v.dart';
import 'v_2_v.dart';

class VipV extends StatefulWidget {
  const VipV({super.key});

  @override
  State<VipV> createState() => _VipVState();
}

class _VipVState extends State<VipV> {
  ProductModel? _chooseProduct;

  late VipSource from;

  bool showBack = false;

  List<ProductModel>? list = [];

  late Future<void> _future;

  @override
  void initState() {
    super.initState();
    from = Get.arguments;
    _future = _loadData();
    // _testData();

    logEvent(ICache().isBusy ? 't_vipb' : 't_vipa');

    if (ICache().isBusy) {
      Future.delayed(const Duration(seconds: 3), () {
        showBack = true;
        refreshState();
      });
    } else {
      showBack = true;
    }
  }

  void refreshState() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadData() async {
    await InfoHeper().getIdfa();

    await IAPUtil().queryProducts();

    list = IAPUtil().subscriptionList;

    _chooseProduct = IAPUtil().subscriptionList.firstWhereOrNull((e) => e.defaultSku == true);

    refreshState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: Colors.black,
        appBar: AppBar(
          elevation: 0,
          leadingWidth: 200,
          scrolledUnderElevation: 0.0,
          backgroundColor: Colors.transparent,
          actions: [
            showBack
                ? IconButton(
                    onPressed: () {
                      Get.back();
                    },
                    icon: Image.asset(Assets.images.newUi.navClose.path, width: 24),
                  )
                : const SizedBox()
          ],
          leading: GestureDetector(
            onTap: () {
              IAPUtil().restore();
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 16),
                Container(
                  height: 24,
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(width: 1, color: IColor.n10),
                  ),
                  child: Text(LocaleKeys.restore.tr, style: ITStyle.l5reg(IColor.n10)),
                ),
                const SizedBox(width: 16),
              ],
            ),
          ),
        ),
        body: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: ICache().isBusy ? Assets.images.newUi.vipBg2.image() : Assets.images.newUi.vipBg1.image(),
            ),
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black,
                      Colors.black,
                    ],
                    stops: [0, 0.6, 1.0],
                  ),
                ),
              ),
            ),
            SafeArea(
              child: FutureBuilder<void>(
                future: _future,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(child: Text('Error: ${snapshot.error}'));
                  }

                  return ICache().isBusy
                      ? V2V(
                          list: list,
                          onTap: (ProductModel model) {
                            _chooseProduct = model;
                          },
                          onTapBuy: () {
                            logEvent('c_vipb_subs');
                            if (_chooseProduct != null) {
                              IAPUtil().buy(_chooseProduct!, vipFrom: from);
                            }
                          },
                        )
                      : V1V(
                          list: list,
                          onTap: (ProductModel model) {
                            _chooseProduct = model;
                          },
                          onTapBuy: () {
                            logEvent('c_vipa_subs');
                            if (_chooseProduct != null) {
                              IAPUtil().buy(_chooseProduct!, vipFrom: from);
                            }
                          },
                        );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
