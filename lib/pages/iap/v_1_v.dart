import 'package:bushy/data/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import '../../generated/locales.g.dart';
import '../../other/i_config.dart';
import '../../other/i_theme.dart';
import '../../other/iap_util.dart';
import 'v_1_is.dart';
import 'v_desc.dart';

class V1V extends StatefulWidget {
  const V1V({
    super.key,
    required this.onTap,
    required this.list,
    required this.onTapBuy,
  });

  final List<ProductModel>? list;
  final void Function(ProductModel product) onTap;
  final void Function() onTapBuy;

  @override
  State<V1V> createState() => _V1VState();
}

class _V1VState extends State<V1V> {
  ProductModel? chooseProduct;

  @override
  void initState() {
    super.initState();

    chooseProduct = IAPUtil().subscriptionList.firstWhereOrNull((e) => e.defaultSku == true);
  }

  @override
  Widget build(BuildContext context) {
    final emojis = ['😃', '🥳', '💎', '👏'];
    final text = LocaleKeys.endless_chatting.tr;
    List<String> strList = text.split('\n');

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 144),
          VDesc(
            strList: strList,
            emojis: emojis,
            title: LocaleKeys.upgrade_to_vip.tr,
          ),
          const SizedBox(height: 16),
          (widget.list != null && widget.list!.isNotEmpty)
              ? V1IS(
                  list: widget.list!,
                  onTap: widget.onTap,
                  onTapBuy: widget.onTapBuy,
                  chooseProduct: chooseProduct,
                )
              : Center(
                  child: Text(
                    LocaleKeys.no_available_products.tr,
                    style: ITStyle.l4reg(IColor.n10),
                  ),
                ),
        ],
      ),
    );
  }
}
