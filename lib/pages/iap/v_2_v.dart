import 'package:bushy/data/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../generated/locales.g.dart';
import '../../other/i_theme.dart';
import 'policy_s.dart';
import 'v_2_is.dart';
import 'v_2_t_v.dart';
import 'v_b.dart';
import 'v_desc.dart';

class V2V extends StatefulWidget {
  const V2V({
    super.key,
    required this.list,
    required this.onTap,
    required this.onTapBuy,
  });

  final List<ProductModel>? list;
  final void Function(ProductModel product) onTap;
  final void Function() onTapBuy;

  @override
  State<V2V> createState() => _V2VState();
}

class _V2VState extends State<V2V> {
  final emojis = ['😃', '🥳', '💎', '👏', '🔥', '❤️'];
  List<String> strList = [];

  ProductModel? chooseProduct;

  @override
  void initState() {
    super.initState();

    chooseProduct = widget.list?.firstWhereOrNull((e) => e.defaultSku == true);

    if (chooseProduct != null) {
      _changeStrList(chooseProduct!);
    }
  }

  void _onTapItem(ProductModel product) {
    chooseProduct = product;
    _changeStrList(product);
    widget.onTap.call(product);
  }

  void _changeStrList(ProductModel modle) {
    // 获取对应的gems值
    final gems = modle.number.toString();
    strList = _splitTextForGems(gems);
    setState(() {});
  }

  /// 提取文本分割的公共方法
  List<String> _splitTextForGems(String gems) {
    final text = LocaleKeys.call_ai_girlfriend.trParams({'gems': gems});
    return text.split('\n');
  }

  @override
  Widget build(BuildContext context) {
    final list = widget.list;
    final hasProducts = list != null && list.isNotEmpty;

    return Stack(
      children: [
        Positioned.fill(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const SizedBox(height: 144),
                VDesc(
                  strList: strList,
                  emojis: emojis,
                  title: LocaleKeys.fifty_percent_off.tr,
                  subtitle: LocaleKeys.best_chat_experience.tr,
                ),
                const SizedBox(height: 16),
                hasProducts
                    ? V2IS(
                        list: list,
                        chooseProduct: chooseProduct,
                        onTap: _onTapItem,
                        onTapBuy: widget.onTapBuy,
                      )
                    : Center(
                        child: Text(
                          LocaleKeys.no_available_products.tr,
                          style: ITStyle.l4reg(IColor.n10),
                        ),
                      ),
              ],
            ),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 30),
              if (hasProducts) const V2TV(),
              if (hasProducts) const SizedBox(height: 12),
              if (hasProducts) VBtn(onTap: widget.onTapBuy),
              const SizedBox(height: 12),
              const Policys(type: PolicyBottomType.vip2),
              const SizedBox(height: 12),
            ],
          ),
        )
      ],
    );
  }
}
