import 'package:bushy/data/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_ext.dart';
import '../../other/i_theme.dart';

class V2IS extends StatefulWidget {
  const V2IS({
    super.key,
    required this.list,
    required this.onTap,
    this.chooseProduct,
    required this.onTapBuy,
  });

  final List<ProductModel> list;
  final void Function(ProductModel product) onTap;
  final ProductModel? chooseProduct;
  final void Function() onTapBuy;

  @override
  State<V2IS> createState() => _V2ISState();
}

class _V2ISState extends State<V2IS> {
  ProductModel? chooseProduct;

  @override
  void initState() {
    super.initState();

    chooseProduct = widget.chooseProduct;
  }

  @override
  Widget build(BuildContext context) {
    final list = widget.list;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: List.generate(
          list.length,
          (index) {
            ProductModel product = list[index];
            if (index == list.length - 1) {
              return _buildItem(product);
            }
            return Padding(
              padding: const EdgeInsetsDirectional.only(end: 8),
              child: _buildItem(product),
            );
          },
        ),
      ),
    );
  }

  Widget _buildItem(ProductModel model) {
    final product = model.productDetails;
    final symbol = product?.currencySymbol;
    var title = '';
    bool isLifeTime = false;
    var showPrice = '';
    final rawPrice = product?.rawPrice ?? 0;

    if (model.isWeek) {
      title = LocaleKeys.weekly.tr;
      showPrice = product?.price ?? '';
    } else if (model.isYear) {
      title = '${LocaleKeys.yearly.tr} ${product?.price}';
      final price = numFixed(rawPrice / 48, position: 2);
      showPrice = '$symbol$price';
    } else if (model.isMonth) {
      title = '${LocaleKeys.monthly.tr} ${product?.price}';
      final price = numFixed(rawPrice / 4, position: 2);
      showPrice = '$symbol$price';
    } else if (model.isLifeTime) {
      title = LocaleKeys.life_time.tr;

      isLifeTime = true;
      var pric = numFixed(rawPrice * 6, position: 2);

      showPrice = '/$symbol$pric';
    }

    // 获取对应的gems值
    final gems = model.number;

    var isChoosed = model.sku == chooseProduct?.sku;
    final bestChoice = model.tag == 1;

    return InkWell(
      splashColor: Colors.transparent, // 去除水波纹
      highlightColor: Colors.transparent, // 去除点击高亮
      onTap: () {
        chooseProduct = model;
        widget.onTap(model);
        setState(() {});
      },
      child: Stack(
        alignment: AlignmentDirectional.topEnd,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 20),
            constraints: const BoxConstraints(minWidth: 110),
            decoration: BoxDecoration(
              color: IColor.n02,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: isChoosed ? const Color(0xffECCDFF) : Colors.transparent),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.end,
                      style: ITStyle.l5reg(IColor.n08),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                isLifeTime
                    ? Row(
                        children: [
                          Text(
                            product?.price ?? '',
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.end,
                            style: ITStyle.l3sem(IColor.n09),
                          ),
                          Text(
                            showPrice,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.end,
                            style: GoogleFonts.montserrat(
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xff999999),
                              decoration: TextDecoration.lineThrough,
                              decorationColor: const Color(0xff999999),
                              decorationThickness: 1.5,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      )
                    : Text(
                        showPrice,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.end,
                        style: ITStyle.l3sem(IColor.n09),
                      ),
                const SizedBox(height: 4),
                isLifeTime
                    ? Row(
                        children: [
                          Assets.images.newUi.gold.image(width: 16),
                          Text(
                            '+$gems',
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.end,
                            style: ITStyle.l5sem(const Color(0xffECCDFF)),
                          ),
                        ],
                      )
                    : Text(
                        LocaleKeys.per_week.tr,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.end,
                        style: ITStyle.l6reg(IColor.n09),
                      ),
              ],
            ),
          ),
          if (bestChoice)
            Container(
              height: 20,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: const BoxDecoration(
                borderRadius: BorderRadiusDirectional.only(
                  topEnd: Radius.circular(16),
                  bottomStart: Radius.circular(16),
                ),
                gradient: LinearGradient(
                  colors: [
                    Color(0xFFCED4FC),
                    Color(0xFFDFCFFB),
                    Color(0xFFECCDFF),
                  ],
                  stops: [
                    0.0306, // 3.06%
                    0.5406, // 54.06%
                    0.9865, // 98.65%
                  ],
                ),
              ),
              child: Center(
                child: Text(
                  LocaleKeys.best_choice.tr,
                  style: ITStyle.l7reg(Color(0xFF333333)),
                ),
              ),
            )
        ],
      ),
    );
  }
}
