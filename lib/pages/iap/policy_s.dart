import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../generated/locales.g.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';

enum PolicyBottomType {
  gems,
  vip1,
  vip2,
}

class Policys extends StatelessWidget {
  const Policys({super.key, required this.type});

  final PolicyBottomType type;

  @override
  Widget build(BuildContext context) {
    // 通过一个方法来简化不同 type 的渲染
    switch (type) {
      case PolicyBottomType.gems:
        return _buildGemsBottom();
      case PolicyBottomType.vip1:
        return _buildVipBottom(IColor.n03, true);
      case PolicyBottomType.vip2:
        return _buildVipBottom(const Color(0xFFA8A8A8), false);
    }
  }

  // 提取公共逻辑，减少重复
  Widget _buildVipBottom(Color buttonColor, bool showSubscriptionText) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildButton(LocaleKeys.privacy.tr, () => IRouter.toPrivacy(), buttonColor),
            _buildSeparator(),
            _buildButton(LocaleKeys.terms.tr, () => IRouter.toTerms(), buttonColor),
          ],
        ),
        if (showSubscriptionText) ...[
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              LocaleKeys.subscription_auto_renew.tr,
              style: ITStyle.l6reg(IColor.n03),
              textAlign: TextAlign.center,
            ),
          ),
        ]
      ],
    );
  }

  Widget _buildGemsBottom() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildButton(LocaleKeys.terms_of_use.tr, () => IRouter.toTerms(), null),
        _buildSeparator(),
        _buildButton(LocaleKeys.privacy_policy.tr, () => IRouter.toPrivacy(), null),
      ],
    );
  }

  // 提取分隔符部分
  Widget _buildSeparator() {
    return Container(
      width: 1,
      height: 12,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      color: IColor.white2,
    );
  }

  Widget _buildButton(String title, VoidCallback onTap, Color? color) {
    return GestureDetector(
      onTap: onTap,
      child: Text(
        title,
        textAlign: TextAlign.center,
        style: GoogleFonts.montserrat(
          fontSize: 10,
          color: color ?? IColor.n05,
          fontWeight: FontWeight.w500,
          decoration: TextDecoration.underline,
          decorationColor: color ?? IColor.n05,
          decorationThickness: 1.0,
        ),
      ),
    );
  }
}
