import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_cache.dart';
import '../../other/i_theme.dart';

class QAList extends StatelessWidget {
  const QAList({super.key});

  @override
  Widget build(BuildContext context) {
    final str = ICache().isBusy ? LocaleKeys.text_message_cost.tr : LocaleKeys.text_message_call_cost.tr;
    List<String> strList = str.split('\n');

    // // 计算文本列表中最长文本的宽度
    // double textWidth = _calculateMaxTextWidth(context, strList);

    // // 计算文本左边距
    // double leftMargin = _calculateLeftMargin(context, textWidth);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          margin: const EdgeInsets.symmetric(horizontal: 37),
          decoration: BoxDecoration(
            color: IColor.n01,
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
              colors: IColor.brandjb,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              strList.length,
              (index) {
                return Column(
                  children: [
                    _buildListItem(strList[index], 0),
                    const SizedBox(height: 12),
                  ],
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 26),
        GestureDetector(
          child: Assets.images.newUi.closeWhite.image(width: 24),
          onTap: () => SmartDialog.dismiss(),
        ),
      ],
    );
  }

  // // 计算文本宽度，选择适当的最大文本宽度
  // double _calculateMaxTextWidth(BuildContext context, List<String> strList) {
  //   double maxTextWidth = MediaQuery.of(context).size.width - 32 - 8 - 32;

  //   double textWidth = 0.0;
  //   for (var str in strList) {
  //     final textPainter = TextPainter(
  //       text: TextSpan(text: str, style: AppTestStyle.l5reg(AppColor.n10)),
  //       textDirection: TextDirection.ltr,
  //     )..layout();
  //     textWidth = textPainter.size.width > textWidth ? textPainter.size.width : textWidth;
  //   }

  //   // 比较 textWidth 和 maxTextWidth，确保不会超出屏幕宽度
  //   return textWidth > maxTextWidth ? maxTextWidth : textWidth;
  // }

  // // 计算左边距
  // double _calculateLeftMargin(BuildContext context, double textWidth) {
  //   // 使用 textWidth 来计算左边距
  //   return (MediaQuery.of(context).size.width - textWidth - 32 - 8 - 32) / 2;
  // }

  // 构建每一行的列表项
  Widget _buildListItem(String text, double leftMargin) {
    return Container(
      // margin: const EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 12),
      // padding: EdgeInsets.only(left: leftMargin), // 左边距
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.start, // 左对齐
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Assets.images.newUi.gold.image(width: 24),
          const SizedBox(width: 8),
          Text(
            text,
            style: ITStyle.l5reg(IColor.n02),
          ),
        ],
      ),
    );
  }
}
