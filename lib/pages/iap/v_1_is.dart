import 'package:bushy/data/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../generated/locales.g.dart';
import '../../other/i_theme.dart';
import 'policy_s.dart';
import 'v_b.dart';

class V1IS extends StatefulWidget {
  const V1IS({
    super.key,
    required this.list,
    required this.onTap,
    required this.onTapBuy,
    this.chooseProduct,
  });

  final List<ProductModel> list;
  final void Function(ProductModel product) onTap;
  final void Function() onTapBuy;
  final ProductModel? chooseProduct;

  @override
  State<V1IS> createState() => _V1ISState();
}

class _V1ISState extends State<V1IS> {
  ProductModel? chooseProd;
  @override
  void initState() {
    super.initState();

    chooseProd = widget.chooseProduct;

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final list = widget.list;
    final price = chooseProd?.productDetails?.price ?? '0.0';

    String unit = '';
    if (chooseProd?.isWeek == true) {
      unit = LocaleKeys.week.tr;
    } else if (chooseProd?.isMonth == true) {
      unit = LocaleKeys.month.tr;
    } else if (chooseProd?.isYear == true) {
      unit = LocaleKeys.year.tr;
    } else if (chooseProd?.isLifeTime == true) {
      unit = LocaleKeys.life_time.tr;
    }

    return Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: List.generate(
              list.length,
              (index) {
                final model = list[index];
                var isChoosed = model.sku == chooseProd?.sku;
                var title = '';
                if (model.isWeek) {
                  title = LocaleKeys.weekly.tr;
                } else if (model.isMonth) {
                  title = LocaleKeys.monthly.tr;
                } else if (model.isYear) {
                  title = LocaleKeys.yearly.tr;
                } else if (model.isLifeTime) {
                  title = LocaleKeys.life_time.tr;
                }

                if (index == list.length - 1) {
                  return _buildItem(model, isChoosed, title);
                }
                return Padding(
                  padding: const EdgeInsetsDirectional.only(end: 8),
                  child: _buildItem(model, isChoosed, title),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            chooseProd?.isLifeTime == true
                ? LocaleKeys.vip_price_lt_desc.trParams({'price': price})
                : LocaleKeys.subscription_info.trParams({'price': price, 'unit': unit}),
            style: ITStyle.l6reg(IColor.n03),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 12),
        VBtn(onTap: widget.onTapBuy),
        const SizedBox(height: 12),
        const Policys(type: PolicyBottomType.vip1),
      ],
    );
  }

  Widget _buildItem(ProductModel model, bool isChoosed, String title) {
    return InkWell(
      splashColor: Colors.transparent, // 去除水波纹
      highlightColor: Colors.transparent, // 去除点击高亮
      onTap: () {
        chooseProd = model;
        widget.onTap(model);
        setState(() {});
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 20),
        constraints: const BoxConstraints(minWidth: 110),
        decoration: BoxDecoration(
          color: IColor.n02,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: isChoosed ? const Color(0xffECCDFF) : Colors.transparent),
        ),
        child: Column(
          children: [
            Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.end,
              style: ITStyle.l5reg(IColor.n08),
            ),
            const SizedBox(width: 16),
            Text(
              model.productDetails?.price ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.end,
              style: ITStyle.l2sem(IColor.n10),
            )
          ],
        ),
      ),
    );
  }
}
