import 'dart:math' as math;

import 'package:bushy/data/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_theme.dart';

class CoinIS extends StatefulWidget {
  const CoinIS({super.key, required this.list, this.chooseProduct, required this.onTap});

  final List<ProductModel> list;
  final ProductModel? chooseProduct;
  final void Function(ProductModel product) onTap;

  @override
  State<CoinIS> createState() => _CoinISState();
}

class _CoinISState extends State<CoinIS> {
  ProductModel? _chooseProduct;

  // 根据折扣百分比获取对应的本地化字符串
  String getDiscount(int discountPercent) {
    try {
      return LocaleKeys.save_num.trParams({'num': discountPercent.toString()});
    } catch (e) {
      // 如果出错，返回硬编码的英文格式
      return 'Save $discountPercent%';
    }
  }

  @override
  void initState() {
    super.initState();

    _chooseProduct = widget.chooseProduct;
  }

  @override
  void didUpdateWidget(CoinIS oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当父组件传入的chooseProduct发生变化时，更新_chooseProduct
    if (widget.chooseProduct != oldWidget.chooseProduct) {
      _chooseProduct = widget.chooseProduct;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.list.isEmpty) {
      return Center(
        child: SizedBox(
          height: 100,
          child: Center(
            child: Text(
              LocaleKeys.no_available_products.tr,
              textAlign: TextAlign.center,
              style: const TextStyle(color: IColor.n05),
            ),
          ),
        ),
      );
    }
    return _buildItems();
  }

  Widget _buildItems() {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (BuildContext context, int index) {
        final item = widget.list[index];
        final bestChoice = item.tag == 1;
        final isSelected = _chooseProduct?.sku == item.sku;

        // 根据产品信息计算折扣百分比，从90%到0%以20%为步长递减
        // 使用算法计算：90 - (index * 20)，确保不小于0
        int discountPercent = math.max(0, 90 - (index * 20));

        String discount = getDiscount(discountPercent);
        String numericPart = item.number.toString();
        String price = item.productDetails?.price ?? '';

        return ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              _chooseProduct = item;
              setState(() {});

              widget.onTap(item);
            },
            child: Stack(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: IColor.brand2,
                    border: Border.all(
                      color: isSelected ? IColor.brand : Colors.transparent,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Assets.images.newUi.gold.image(width: 32),
                      Text(numericPart, style: ITStyle.l3sem(Colors.black)),
                      const SizedBox(width: 4),
                      Text('Coins', style: ITStyle.l4reg(IColor.n02)),
                      const Spacer(),
                      Column(
                        children: [
                          Text(price, style: ITStyle.l2sem(IColor.n02)),
                          Text(discount, style: ITStyle.l6reg(IColor.n03)),
                        ],
                      ),
                    ],
                  ),
                ),
                if (bestChoice)
                  Row(
                    children: [
                      Container(
                        height: 20,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadiusDirectional.only(
                            topStart: Radius.circular(16),
                            bottomEnd: Radius.circular(16),
                          ),
                          color: IColor.brand,
                        ),
                        child: Center(
                          child: Text(
                            LocaleKeys.best_choice.tr,
                            style: ITStyle.l7reg(Colors.white),
                          ),
                        ),
                      ),
                    ],
                  )
              ],
            ),
          ),
        );
      },
      itemCount: widget.list.length,
      separatorBuilder: (c, i) => const SizedBox(height: 16),
    );
  }
}
