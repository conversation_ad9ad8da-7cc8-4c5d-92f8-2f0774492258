import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../common/gradient_text.dart';
import '../../other/i_theme.dart';
import 'v_r.dart';

class VDesc extends StatelessWidget {
  const VDesc({
    super.key,
    required this.strList,
    required this.emojis,
    required this.title,
    this.subtitle,
  });

  final List<String> strList;
  final List<String> emojis;
  final String title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 30),
      decoration: BoxDecoration(
        color: IColor.black5,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GradientText(
            data: title,
            gradient: const LinearGradient(
              colors: IColor.brandjb,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            style: GoogleFonts.montserrat(fontSize: 32, fontWeight: FontWeight.w800),
          ),
          subtitle != null
              ? Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Text(subtitle!, style: ITStyle.l4sem(IColor.n10)),
                )
              : const SizedBox(),
          const SizedBox(height: 16),
          VR(strList: strList, emojis: emojis),
        ],
      ),
    );
  }
}
