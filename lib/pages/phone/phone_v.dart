import 'package:bushy/pages/phone/p_c.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:loading_indicator/loading_indicator.dart';

import '../../common/role_ana_row.dart';
import '../../common/s_image.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../other/i_theme.dart';

class PhoneV extends StatelessWidget {
  PhoneV({super.key});

  final ctr = Get.put(PC());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: const Color(0xff222222),
      appBar: AppBar(
        leadingWidth: 0,
        leading: const SizedBox(),
        title: Obx(() {
          final callState = ctr.callState.value;
          if (callState == CallState.incoming) {
            return const SizedBox();
          }
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              RoleAnaRow(role: ctr.role),
              _buildTimer(),
            ],
          );
        }),
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: Assets.images.newUi.creatBg.image(width: double.infinity, height: double.infinity),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 300,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Color(0xff140D28),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),
          Positioned.fill(
            child: SafeArea(
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: SImage(
                              width: double.infinity,
                              height: double.infinity,
                              url: ctr.guideVideo?.gifUrl ?? ctr.role.avatar,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: IColor.n10, width: 1),
                            ),
                          ),
                          Align(
                            alignment: Alignment.bottomCenter,
                            child: Obx(
                              () => ctr.callState.value == CallState.answered
                                  ? LoadingAnimationWidget.staggeredDotsWave(color: Colors.white, size: 40)
                                  : const SizedBox(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 184,
                    child: _buildContent(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHangButton() {
    return _buildBtn(
      onTap: ctr.onTapHangup,
      icon: Assets.images.newUi.hangup.image(width: 60),
      title: LocaleKeys.decline.tr,
    );
  }

  Widget _buildBtn({
    required Function() onTap,
    required Widget icon,
    required String title,
  }) {
    return InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          const SizedBox(height: 4),
          Text(
            title,
            style: ITStyle.l5sem(Colors.white),
          )
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Obx(() {
      final callState = ctr.callState.value;

      switch (callState) {
        case CallState.incoming:
          return _buildIncoming();
        case CallState.calling:
          return _buildCalling();
        case CallState.listening:
          return _buildListening();
        case CallState.answering:
          return _buildAnswering();
        case CallState.answered:
          return _buildListening();
      }
    });
  }

  Widget _buildListening() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _buildText(),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: _buildHangButton(),
            ),
            const SizedBox(width: 135),
            Stack(
              alignment: Alignment.topCenter,
              children: [
                SizedBox(
                  width: 80,
                  height: 80,
                  child: ctr.micOn.value
                      ? const LoadingIndicator(
                          indicatorType: Indicator.ballScale,
                          colors: [Color(0xff906BF7)],
                          strokeWidth: 2,
                        )
                      : null,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: _buildBtn(
                    onTap: () => ctr.onTapMic(!ctr.micOn.value),
                    icon: ctr.micOn.value
                        ? Assets.images.newUi.micOn.image(width: 60) //
                        : Assets.images.newUi.micOff.image(width: 60),
                    title: ctr.micOn.value
                        ? LocaleKeys.mic_on.tr //
                        : LocaleKeys.mic_off.tr,
                  ),
                ),
              ],
            )
          ],
        ),
      ],
    );
  }

  Widget _buildAnswering() {
    return Column(
      children: [
        const SizedBox(height: 24),
        Text(
          LocaleKeys.waiting_response.tr,
          style: ITStyle.l5sem(Colors.white),
        ),
        const SizedBox(height: 24),
        _buildHangButton(),
      ],
    );
  }

  Widget _buildCalling() {
    return _buildHangButton();
  }

  Widget _buildIncoming() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildHangButton(),
        const SizedBox(width: 135),
        _buildBtn(
          onTap: ctr.onTapAccept,
          icon: Assets.images.newUi.accept.image(width: 60),
          title: LocaleKeys.accept.tr,
        ),
      ],
    );
  }

  Widget _buildText() {
    if (ctr.answerText.isEmpty) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      child: Center(
        child: Text(
          ctr.answerText,
          maxLines: 3,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: ITStyle.l4reg(null),
        ),
      ),
    );
  }

  Widget _buildTimer() {
    return Obx(() {
      if (ctr.showFormattedDuration.value) {
        return Container(
          width: 72,
          height: 24,
          margin: const EdgeInsetsDirectional.only(start: 12),
          decoration: BoxDecoration(
            color: const Color(0x33906BF7),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Color(0xffDFCFFB),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                ctr.formattedDuration(ctr.callDuration.value),
                style: ITStyle.l4reg(IColor.n10),
              ),
            ],
          ),
        );
      }
      return const SizedBox();
    });
  }
}
