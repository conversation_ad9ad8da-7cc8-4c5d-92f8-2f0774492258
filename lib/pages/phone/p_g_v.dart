import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

import '../../common/bg_blur.dart';
import '../../common/role_ana_row.dart';
import '../../common/s_image.dart';
import '../../data/chater.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import '../../other/nav_obs.dart';
import '../../other/user_helper.dart';

class PGV extends StatefulWidget {
  const PGV({super.key});

  @override
  State<PGV> createState() => _PGVState();
}

class _PGVState extends State<PGV> with RouteAware, WidgetsBindingObserver {
  late Chater role;

  late VideoPlayerController? _controller;
  late Future<void> _initializeVideoPlayerFuture;

  bool _isPlayed = false;

  @override
  void initState() {
    super.initState();
    var args = Get.arguments;
    role = args['role'];

    WidgetsBinding.instance.addObserver(this);

    _initVideoPlay();
  }

  void _initVideoPlay() async {
    final guide = role.characterVideoChat?.firstWhereOrNull((e) => e.tag == 'guide');
    var url = guide?.url;

    _controller = VideoPlayerController.networkUrl(Uri.parse(url ?? ''));

    _initializeVideoPlayerFuture = _controller!.initialize().then((_) {
      _controller?.addListener(_videoListener);

      Future.delayed(const Duration(seconds: 5), () {
        if (mounted) {
          _controller?.play();
          setState(() {});
        }
      });
      setState(() {});
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    /// 路由订阅
    NavObs().routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    /// 取消路由订阅
    NavObs().routeObserver.unsubscribe(this);

    WidgetsBinding.instance.removeObserver(this);

    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didPushNext() {
    // 页面被其他页面覆盖时调用
    debugPrint('ChatPage pushed to the background');
    _controller?.pause();
  }

  @override
  void didPopNext() {
    // 页面从其他页面回到前台时调用
    debugPrint('ChatPage resumed from the background');
    _controller?.play();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _controller?.pause();
      setState(() {});
    }
    if (state == AppLifecycleState.resumed) {
      _controller?.play();
      setState(() {});
    }
  }

  void _videoListener() {
    if (_controller == null) return;
    if (_controller!.value.isPlaying) {
      setState(() {});
    }

    final position = _controller!.value.position;
    final duration = _controller!.value.duration;
    final timeRemaining = duration - position;

    if (timeRemaining <= const Duration(milliseconds: 500)) {
      _isPlayed = true;
      _controller?.pause();
      setState(() {});

      if (_isPlayed && UserHelper().isVip.value) {
        if (UserHelper().balance.value < ConsumeSource.call.gems) {
          IRouter.offGem(ConsumeSource.call);
          return;
        }
        IRouter.offPhone(role: role, showVideo: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: SImage(url: role.avatar),
          ),
          FutureBuilder(
            future: _initializeVideoPlayerFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                return Positioned.fill(
                  child: FittedBox(
                    fit: BoxFit.cover,
                    child: SizedBox(
                      width: _controller?.value.size.width,
                      height: _controller?.value.size.height,
                      child: VideoPlayer(_controller!),
                    ),
                  ),
                );
              } else {
                return const Center(child: CircularProgressIndicator(color: IColor.brand));
              }
            },
          ),
          Positioned.fill(
            child: SafeArea(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      InkWell(
                        onTap: () => Get.back(),
                        child: SizedBox(
                          width: 44,
                          height: 44,
                          child: Center(child: Assets.images.newUi.navCloseBlack.image(width: 20)),
                        ),
                      ),
                      const SizedBox(width: 4),
                    ],
                  ),
                  const Spacer(),
                  Obx(() {
                    final vip = UserHelper().isVip.value;
                    if (_isPlayed) {
                      if (vip) {
                        return const SizedBox();
                      }
                      return _buildVideoView();
                    }
                    return _buildWattingView();
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: BgBlur(
        child: _buildVideoChild(),
      ),
    );
  }

  Widget _buildVideoChild() {
    return Stack(
      children: [
        Column(
          children: [
            RoleAnaRow(role: role),
            const SizedBox(height: 20),
            Text(
              LocaleKeys.activate_benefits.tr,
              textAlign: TextAlign.center,
              style: ITStyle.l3sem(Colors.white),
            ),
            const SizedBox(height: 28),
            Text(
              LocaleKeys.get_ai_interactive_video_chat.tr,
              textAlign: TextAlign.center,
              style: ITStyle.l5reg(Colors.white),
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () {
                IRouter.pushVip(VipSource.call);
              },
              child: Container(
                height: 32,
                margin: const EdgeInsets.symmetric(horizontal: 34),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: IColor.brand,
                ),
                child: Center(
                  child: Text(
                    LocaleKeys.continue_txt.tr,
                    style: ITStyle.l4sem(Colors.white),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWattingView() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: BgBlur(
        child: _buildWaitChild(),
      ),
    );
  }

  Widget _buildWaitChild() {
    return Column(
      children: [
        RoleAnaRow(role: role),
        const SizedBox(height: 20),
        Text(
          LocaleKeys.invites_you_to_video_call.tr,
          textAlign: TextAlign.center,
          style: ITStyle.l3reg(Colors.white),
        ),
        const SizedBox(height: 16),
        InkWell(
          onTap: () => Get.back(),
          child: Assets.images.newUi.hangup.image(width: 72),
        ),
      ],
    );
  }
}
