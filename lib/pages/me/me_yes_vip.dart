import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../common/gradient_text.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_ext.dart';
import '../../other/i_theme.dart';
import '../../other/user_helper.dart';

class MeYesVip extends StatelessWidget {
  const MeYesVip({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 180,
      child: Stack(
        children: [
          PositionedDirectional(
            top: 47,
            start: 0,
            end: 0,
            bottom: 0,
            child: Assets.images.newUi.beVipBg.image(fit: BoxFit.cover),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 63),
                GradientText(
                  data: LocaleKeys.vip_member.tr,
                  gradient: const LinearGradient(
                    colors: IColor.brandjb,
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Obx(() {
                  UserHelper().isVip.value;
                  final timer = UserHelper().user?.subscriptionEnd ?? DateTime.now().millisecondsSinceEpoch;
                  final date = formatTimestamp(timer);

                  return Text(
                    LocaleKeys.deadline.trParams({'date': date}),
                    textAlign: TextAlign.center,
                    style: GoogleFonts.montserrat(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  );
                }),
                const SizedBox(height: 12),
              ],
            ),
          ),
          PositionedDirectional(
            bottom: 0,
            end: 0,
            child: Directionality(
              // 修正1：移除.textDirection
              textDirection: Directionality.of(context),
              child: Transform.scale(
                // 修正2：直接比较文本方向枚举值
                scaleX: Directionality.of(context) == TextDirection.rtl ? -1 : 1,
                transformHitTests: true,
                alignment: Alignment.center,
                child: Assets.images.newUi.beVipRole.image(width: 118, height: 180, fit: BoxFit.cover),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
