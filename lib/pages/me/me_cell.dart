import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../other/i_theme.dart';

class MeCell extends StatelessWidget {
  const MeCell({
    super.key,
    required this.title,
    this.onTap,
    this.showLine = true,
    this.showchevronRight = true,
    this.subtitle,
  });

  final String title;
  final Widget? subtitle;
  final Function()? onTap;
  final bool showLine;
  final bool showchevronRight;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.montserrat(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: IColor.n02,
                    ),
                  ),
                ),
                if (subtitle != null) subtitle!,
                showchevronRight
                    ? const Icon(
                        Icons.chevron_right,
                        color: Colors.black,
                        size: 20,
                      )
                    : SizedBox.shrink()
              ],
            ),
          ),
          if (showLine)
            Container(
              height: 1,
              color: const Color(0xffF2F2F2),
            ),
        ],
      ),
    );
  }
}
