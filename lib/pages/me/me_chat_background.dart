import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_theme.dart';

class MeSCBackground extends StatelessWidget {
  const MeSCBackground({
    super.key,
    required this.onTapUpload,
    required this.onTapUseChat,
    required this.isUseChater,
  });

  final VoidCallback onTapUpload;
  final VoidCallback onTapUseChat;
  final bool isUseChater;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          LocaleKeys.set_chat_background.tr,
          textAlign: TextAlign.center,
          style: ITStyle.l3sem(IColor.n02),
        ),
        const SizedBox(height: 24),
        _buildButton(LocaleKeys.upload_a_photo.tr, onTapUpload),
        const SizedBox(height: 8),
        isUseChater
            ? _buildSelectButton(LocaleKeys.use_ai_characters_cover.tr)
            : _buildButton(LocaleKeys.use_ai_characters_cover.tr, onTapUseChat),
      ],
    );
  }

  Widget _buildButton(String title, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 32,
        margin: const EdgeInsets.symmetric(horizontal: 60),
        decoration: const BoxDecoration(
          color: IColor.brand,
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
        child: Center(
          child: Text(
            title,
            style: ITStyle.l4sem(IColor.n10),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectButton(String title) {
    return Container(
      height: 32,
      margin: const EdgeInsets.symmetric(horizontal: 60),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        border: Border.all(
          color: const Color(0xffA8A8A8),
          width: 1.0,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: ITStyle.l4sem(const Color(0xffA8A8A8)),
          ),
          const SizedBox(width: 8),
          Assets.images.newUi.check.image(width: 20),
        ],
      ),
    );
  }
}
