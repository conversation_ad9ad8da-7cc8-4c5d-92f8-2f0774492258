import 'dart:io';

import 'package:bushy/other/event_util.dart';
import 'package:bushy/service/event_api.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../common/i_dialog.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../main.dart'; // 导入 main.dart 以使用 supportedLocales
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import '../../other/info_helper.dart';
import '../../other/user_helper.dart';
import 'me_be_vip.dart';
import 'me_cell.dart';
import 'me_chat_background.dart';
import 'me_title.dart';
import 'me_yes_vip.dart';

class MeV extends StatefulWidget {
  const MeV({super.key});

  @override
  State<MeV> createState() => _MeVState();
}

class _MeVState extends State<MeV> {
  final FocusNode _focusNode = FocusNode();
  late TextEditingController _textEditingController;
  String _version = '';
  String _chatbgImagePath = '';
  String _nickname = '';

  @override
  void initState() {
    super.initState();

    _nickname = UserHelper().user?.nickname ?? '';
    _loadData();
  }

  void _loadData() async {
    final v = await InfoHeper().version();
    final n = await InfoHeper().buildNumber();
    _version = '$v  $n';

    _chatbgImagePath = ICache().cbi;
    setState(() {});
  }

  void _changeNickName() {
    _nickname = UserHelper().user?.nickname ?? '';
    _textEditingController = TextEditingController(text: _nickname);

    IDialog.input(
      title: LocaleKeys.your_nickname.tr,
      hintText: LocaleKeys.input_your_nickname.tr,
      focusNode: _focusNode,
      textEditingController: _textEditingController,
      onConfirm: _updateUserInfo,
    );
  }

  void _updateUserInfo() async {
    var nickname = _textEditingController.text.trim();

    if (nickname.isEmpty) {
      SmartDialog.showToast(LocaleKeys.input_your_nickname.tr);
      return;
    }
    SmartDialog.showLoading();
    await UserHelper().updateUser(nickname);
    SmartDialog.dismiss();

    _nickname = UserHelper().user?.nickname ?? '';
    _focusNode.unfocus();
    SmartDialog.dismiss(tag: DialogTag.nickname.name);
    setState(() {});
  }

  void _resetChatBackground() async {
    ICache().cbi = '';
    _chatbgImagePath = '';
    SmartDialog.dismiss();

    SmartDialog.showNotify(
      msg: LocaleKeys.reset_chat_background_success.tr,
      notifyType: NotifyType.success,
    );
  }

  void _changeChatBackground() async {
    IDialog.sheet(
      MeSCBackground(
        onTapUpload: _changeChatBackgroundChooseImage,
        onTapUseChat: _resetChatBackground,
        isUseChater: _chatbgImagePath.isEmpty,
      ),
    );
  }

  void _changeChatBackgroundChooseImage() async {
    SmartDialog.dismiss();

    final List<AssetEntity>? result = await AssetPicker.pickAssets(
      context,
      pickerConfig: const AssetPickerConfig(
        maxAssets: 1,
        requestType: RequestType.image,
        themeColor: Color(0xff906bf7),
      ),
    );
    if (result != null && result.isNotEmpty) {
      final iamge = result.first;
      final pickedFile = await iamge.file;
      if (pickedFile != null) {
        final directory = await getApplicationDocumentsDirectory();
        final fileName = path.basename(pickedFile.path);
        final cachedImagePath = path.join(directory.path, fileName);
        final File cachedImage = await File(pickedFile.path).copy(cachedImagePath);
        ICache().cbi = cachedImage.path;
        _chatbgImagePath = cachedImage.path;

        SmartDialog.showNotify(
          msg: LocaleKeys.change_chat_background_success.tr,
          notifyType: NotifyType.success,
        );
      }
    }
  }

  // void _autoTranslation(bool value) async {
  //   log.d('autoTranslation: $value');
  //   if (UserHelper().isVip.value) {
  //     SmartDialog.showLoading();
  //     await IP.updateEventParams(autoTranslate: value);
  //     await UserHelper().getUserInfo();
  //     SmartDialog.dismiss();
  //   } else {
  //     IRouter.pushVip(VipSource.trans);
  //   }
  // }

  final List<Map<String, dynamic>> _languages = [
    {'name': 'English', 'locale': const Locale('en', 'US')}, // 英语（美国）
    {'name': 'العربية', 'locale': const Locale('ar', 'SA')}, // 阿拉伯语（沙特）
    {'name': 'Bahasa Indonesia', 'locale': const Locale('id', 'ID')}, // 印尼语
    {'name': 'Deutsch', 'locale': const Locale('de', 'DE')}, // 德语
    {'name': 'Español', 'locale': const Locale('es', 'ES')}, // 西班牙语
    {'name': 'Filipino', 'locale': const Locale('fil', 'PH')}, // 菲律宾语
    {'name': 'Français', 'locale': const Locale('fr', 'FR')}, // 法语
    {'name': 'Italiano', 'locale': const Locale('it', 'IT')}, // 意大利语
    {'name': '日本語', 'locale': const Locale('ja', 'JP')}, // 日语
    {'name': '한국어', 'locale': const Locale('ko', 'KR')}, // 韩语
    {'name': 'Português (Brasileiro)', 'locale': const Locale('pt', 'BR')}, // 葡萄牙语（巴西）
    {'name': 'Português (Portugal)', 'locale': const Locale('pt', 'PT')}, // 葡萄牙语（葡萄牙）
    {'name': 'ไทย', 'locale': const Locale('th', 'TH')}, // 泰语
    {'name': 'Türkçe', 'locale': const Locale('tr', 'TR')}, // 土耳其语
    {'name': 'Tiếng Việt', 'locale': const Locale('vi', 'VN')}, // 越南语
    {'name': '繁體中文', 'locale': const Locale('zh', 'TW')}, // 繁体中文（台湾）
  ];

  void _changeLanguage() {
    try {
      logEvent('c_language');
      Get.bottomSheet(
        Container(
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.8, // 设置最大高度为屏幕高度的80%
          ),
          decoration: BoxDecoration(
            color: Get.theme.colorScheme.surface,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ..._languages.map((language) => ListTile(
                            leading: const Icon(Icons.language),
                            title: Text(language['name'] as String),
                            trailing: _isCurrentLocale(language['locale'] as Locale)
                                ? const Icon(Icons.check, color: Color(0xff906BF7))
                                : null,
                            onTap: () {
                              _applyLanguage(language['locale'] as Locale);
                            },
                          )),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        isScrollControlled: true,
      );
    } catch (e) {
      debugPrint('语言选择菜单显示错误: $e');
      SmartDialog.showToast('Language selection failed, please try again.');
    }
  }

  // 判断是否是当前语言
  bool _isCurrentLocale(Locale locale) {
    try {
      final currentLocale = Get.locale;
      if (currentLocale == null) return false;

      // 先比较语言和国家代码
      if (locale.countryCode != null && currentLocale.countryCode != null) {
        return currentLocale.languageCode == locale.languageCode && currentLocale.countryCode == locale.countryCode;
      }

      // 如果国家代码缺失，只比较语言代码
      return currentLocale.languageCode == locale.languageCode;
    } catch (e) {
      debugPrint('比较语言出错: $e');
      return false;
    }
  }

  // 应用语言更改
  void _applyLanguage(Locale locale) {
    try {
      Get.back();

      // 从支持的语言列表中找到匹配的语言
      Locale? matchedLocale;
      for (var supportedLocale in supportedLocales) {
        if (supportedLocale.languageCode == locale.languageCode) {
          matchedLocale = supportedLocale;
          break;
        }
      }

      if (matchedLocale != null) {
        Get.updateLocale(matchedLocale);
        // 保存选择的语言
        ICache().locale = matchedLocale;
        UserHelper().localChanged.value++;
      } else {
        // 如果没有找到匹配的语言，使用默认的英语
        Get.updateLocale(supportedLocales.first);
        ICache().locale = supportedLocales.first;
        UserHelper().localChanged.value++;
      }
    } catch (e) {
      debugPrint('更改语言出错: $e');
      SmartDialog.showToast('Failed to change language, please try again.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Assets.images.newUi.navBack.image(width: 24),
        ),
      ),
      body: _buildBody(),
    );
  }

  Stack _buildBody() {
    return Stack(
      children: [
        SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: kToolbarHeight + 8),
              Obx(() {
                return UserHelper().isVip.value ? const MeYesVip() : const MeBeVip();
              }),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white.withOpacity(0.1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeTitle(title: LocaleKeys.settings.tr, fontSize: 32),
                    const SizedBox(height: 4),
                    // Obx(() {
                    //   var isAutoTrans = UserHelper().autoTranslate.value;
                    //   return MeCell(
                    //     title: LocaleKeys.automatic_translation.tr,
                    //     subtitle: SSwitch(
                    //       value: isAutoTrans,
                    //       onChanged: _autoTranslation,
                    //     ),
                    //   );
                    // }),
                    InkWell(
                      onTap: _changeNickName,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Row(
                          children: [
                            Text(
                              LocaleKeys.your_nickname.tr,
                              style: GoogleFonts.montserrat(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: IColor.n02,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                _nickname,
                                textAlign: TextAlign.end,
                                style: ITStyle.l5reg(IColor.n05),
                              ),
                            ),
                            const Icon(
                              Icons.chevron_right,
                              color: Colors.black,
                              size: 20,
                            )
                          ],
                        ),
                      ),
                    ),
                    MeCell(
                      title: LocaleKeys.select_language.tr,
                      subtitle: Text(
                        _languages.firstWhere((language) => language['locale'] == Get.locale)['name'] as String,
                        style: ITStyle.l5reg(IColor.n05),
                      ),
                      onTap: _changeLanguage,
                    ),
                    Container(height: 1, color: const Color(0xffF2F2F2)),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MeTitle(title: LocaleKeys.support.tr),
                        const SizedBox(height: 4),
                        MeCell(
                          title: LocaleKeys.feedback.tr,
                          onTap: () => IRouter.toEmail(),
                        ),
                        MeCell(
                          title: LocaleKeys.set_chat_background.tr,
                          onTap: _changeChatBackground,
                        ),
                        MeCell(
                          title: LocaleKeys.app_version.tr,
                          subtitle: Text(
                            _version,
                            style: ITStyle.l5reg(IColor.n05),
                          ),
                          showchevronRight: false,
                        ),
                        MeTitle(title: LocaleKeys.legal.tr),
                        const SizedBox(height: 4),
                        MeCell(
                          title: LocaleKeys.privacy_policy.tr,
                          onTap: () => IRouter.toPrivacy(),
                        ),
                        MeCell(
                          title: LocaleKeys.terms_of_use.tr,
                          onTap: () => IRouter.toTerms(),
                        ),
                        const SizedBox(height: 8)
                      ],
                    )
                  ],
                ),
              ),
              const SizedBox(height: 34),
              Center(
                child: GestureDetector(
                  onLongPress: () {
                    Get.to(const EventPage());
                  },
                  child: Text(
                    _version,
                    style: ITStyle.l5reg(IColor.n04),
                  ),
                ),
              ),
              const SizedBox(height: 34),
            ],
          ),
        ),
        // Positioned(
        //   top: 0,
        //   left: 0,
        //   right: 0,
        //   child: AppBar(
        //     elevation: 0,
        //     leading: IconButton(
        //       onPressed: () {
        //         Get.back();
        //       },
        //       icon: Assets.images.newUi.navBack.image(width: 24),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
