import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class MeTitle extends StatelessWidget {
  const MeTitle({
    super.key,
    required this.title,
    this.fontSize,
  });

  final String title;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Text(
        title,
        style: GoogleFonts.montserrat(
          fontSize: fontSize ?? 16,
          fontWeight: FontWeight.w600,
          color: const Color(0xff333333),
        ),
      ),
    );
  }
}
