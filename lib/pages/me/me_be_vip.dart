import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';

class MeBeVip extends StatelessWidget {
  const MeBeVip({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 247,
      child: Stack(
        children: [
          PositionedDirectional(
            top: 47,
            start: 0,
            end: 0,
            bottom: 0,
            child: Assets.images.newUi.beVipBg.image(fit: BoxFit.cover),
          ),
          PositionedDirectional(
            top: 60,
            start: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.upgrade_to_vip.tr,
                  style: GoogleFonts.montserrat(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    height: 40 / 32.0,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  LocaleKeys.unlimited_messages.tr,
                  style: GoogleFonts.montserrat(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                    height: 1.6,
                  ),
                ),
                const SizedBox(height: 16),
                _buildButton(),
              ],
            ),
          ),
          PositionedDirectional(
            bottom: 0,
            end: 0,
            child: Directionality(
              // 修正1：移除.textDirection
              textDirection: Directionality.of(context),
              child: Transform.scale(
                // 修正2：直接比较文本方向枚举值
                scaleX: Directionality.of(context) == TextDirection.rtl ? -1 : 1,
                transformHitTests: true,
                alignment: Alignment.center,
                child: Assets.images.newUi.beVipRole.image(width: 166, height: 247, fit: BoxFit.cover),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton() {
    return InkWell(
      onTap: () {
        IRouter.pushVip(VipSource.mevip);
      },
      child: Container(
        height: 32,
        constraints: const BoxConstraints(minWidth: 169),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: IColor.brandjb,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.all(Radius.circular(21)),
        ),
        child: Center(
          child: Text(
            LocaleKeys.explore.tr,
            style: GoogleFonts.montserrat(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: const Color(0xff333333),
            ),
          ),
        ),
      ),
    );
  }
}
