import 'package:bushy/common/i_dialog.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

import '../../common/i_empty.dart';
import '../../data/chater.dart';
import '../../other/i_enum.dart';
import '../../other/net_obs.dart';
import '../../other/user_helper.dart';
import '../../service/i_p.dart';
import '../mian/main_v.dart';
import 'chater_cell.dart';
import 'home_c.dart';
import 'phone_ctr.dart';

class HomeList extends StatefulWidget {
  const HomeList({super.key, required this.type});

  final ChaterListType type;

  @override
  State<HomeList> createState() => _HomeListState();
}

class _HomeListState extends State<HomeList> {
  late final EasyRefreshController _controller;

  String? rendStyl;
  bool? videoChat;
  bool? genVideo;
  bool? genImg;
  bool? collect;
  bool? changeClothing;
  int page = 1;
  int size = 10;
  List<Chater> list = [];

  EmptyType? type = EmptyType.loading;
  bool isNoMoreData = false;

  final homeCtr = Get.find<HomeC>();

  List<int> tagIds = [];

  @override
  void initState() {
    super.initState();

    switch (widget.type) {
      case ChaterListType.realistic:
        rendStyl = widget.type.name;
        break;
      case ChaterListType.anime:
        rendStyl = widget.type.name;
        break;
      case ChaterListType.dressup:
        changeClothing = true;
        break;
      case ChaterListType.video:
        videoChat = true;
        break;

      default:
    }

    _controller = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!NetObs.to.isConnected.value) {
        return;
      }
      await Future.delayed(const Duration(milliseconds: 1000)); // 延迟调用
      _onRefresh();
    });

    ever(homeCtr.filterEvent, (tags) {
      SmartDialog.showLoading();
      final ids = tags.map((e) => e.id!).toList();
      tagIds = ids;
      _onRefresh();
    });

    ever(homeCtr.followEvent, (even) {
      try {
        final e = even.$1;
        final id = even.$2;

        final index = list.indexWhere((element) => element.id == id);
        if (index != -1) {
          list[index].collect = e == FollowEvent.follow;
        }
        setState(() {});
      } catch (e) {}
    });

    ever(UserHelper().localChanged, (_) {
      _onRefresh();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    page = 1;
    await _fetchData();

    await Future.delayed(Duration(milliseconds: 50));
    _controller.finishRefresh();
    _controller.resetFooter();
  }

  Future<void> _onLoad() async {
    page++;
    await _fetchData();

    await Future.delayed(Duration(milliseconds: 50));
    _controller.finishLoad(isNoMoreData ? IndicatorResult.noMore : IndicatorResult.success);
  }

  Future<void> _fetchData() async {
    try {
      UserHelper().getUserInfo();

      final res = await IP.homeList(
        page: page,
        size: size,
        rendStyl: rendStyl,
        tags: tagIds,
        changeClothing: changeClothing,
        genVideo: genVideo,
        videoChat: videoChat,
      );

      isNoMoreData = (res?.records?.length ?? 0) < size;

      if (page == 1) {
        list.clear();

        if (widget.type == ChaterListType.favored && rootIndex == RootIndex.home) {
          final vc = Get.find<PhoneCtr>();
          vc.onCall(res?.records);
        }
      }

      list.addAll(res?.records ?? []);

      type = list.isEmpty ? EmptyType.empty : null;

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      type = list.isEmpty
          ? (NetObs.to.isConnected.value == false ? EmptyType.noNetwork : EmptyType.empty) //
          : type;

      if (page > 1) page--;
      if (mounted) {
        setState(() {});
      }
    } finally {
      SmartDialog.dismiss(status: SmartStatus.loading);
    }
  }

  void _onCollect(int index) async {
    final role = list[index];
    final chatId = role.id;
    if (chatId == null) {
      return;
    }
    if (role.collect == true) {
      final res = await IP.cancelCollectRole(chatId);
      if (res) {
        role.collect = false;
        setState(() {});
      }
    } else {
      final res = await IP.collectRole(chatId);
      if (res) {
        role.collect = true;
        setState(() {});

        if (!IDialog.rateCollectShowd) {
          IDialog.showRateUs(LocaleKeys.help_rate_message.tr);
          IDialog.rateCollectShowd = true;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return EasyRefresh(
      controller: _controller,
      onRefresh: _onRefresh,
      onLoad: _onLoad,
      child: type != null ? IEmpty(type: type!) : _buildList(),
    );
  }

  Widget _buildList() {
    return MasonryGridView.count(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      crossAxisCount: 2,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      itemCount: list.length,
      itemBuilder: (context, index) {
        final role = list[index];
        return SizedBox(
          height: index.isOdd ? 280 : 240,
          child: ChaterCell(
            role: role,
            onCollect: (Chater role) {
              _onCollect(index);
            },
            type: widget.type,
          ),
        );
      },
    );
  }
}
