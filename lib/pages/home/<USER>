import 'package:bushy/common/i_empty.dart';
import 'package:bushy/data/chater.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/pages/home/<USER>';
import 'package:bushy/service/i_p.dart';
import 'package:get/get.dart';

class RSC extends GetxController {
  int page = 1;
  int size = 1000;

  var list = <Chater>[].obs;
  var type = Rx<EmptyType?>(EmptyType.empty);

  var searchQuery = ''.obs;
  var currentRequestId = 0.obs; // 当前请求的 ID

  @override
  void onInit() {
    super.onInit();

    debounce(searchQuery, (_) {
      log.d('搜索: ${searchQuery.value}');
      // 生成一个唯一的请求 ID
      var requestId = DateTime.now().millisecondsSinceEpoch;
      currentRequestId.value = requestId;

      // API 请求
      search(searchQuery.value, requestId);
    }, time: const Duration(milliseconds: 500));
  }

  Future<void> search(String searchText, int requestId) async {
    try {
      if (searchText.isEmpty) {
        list.clear();
        type.value = EmptyType.empty;
        return;
      }

      final res = await IP.homeList(
        page: page,
        size: size,
        name: searchText,
      );

      // 如果当前的请求 ID 不是最新的，就忽略它
      if (requestId != currentRequestId.value) {
        log.d('请求 ID: $requestId 当前的请求 ID 不是最新的，就忽略它');
        return;
      }

      if (page == 1) {
        list.clear();
      }
      type.value = null;
      list.addAll(res?.records ?? []);

      type.value = list.isEmpty ? EmptyType.empty : null;
    } catch (e) {
      type.value = list.isEmpty ? EmptyType.empty : null;
    }
  }

  void onCollect(int index) async {
    final role = list[index];
    final chatId = role.id;
    if (chatId == null) {
      return;
    }
    if (role.collect == true) {
      final res = await IP.cancelCollectRole(chatId);
      if (res) {
        role.collect = false;
        list.refresh();
        Get.find<HomeC>().followEvent.value = (FollowEvent.unfollow, chatId);
      }
    } else {
      final res = await IP.collectRole(chatId);
      if (res) {
        role.collect = true;
        list.refresh();
        Get.find<HomeC>().followEvent.value = (FollowEvent.follow, chatId);
      }
    }
  }
}
