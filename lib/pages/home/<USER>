import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/gradient_painter.dart';
import '../../other/i_enum.dart';
import '../../other/i_theme.dart';
import 'chater_list_view.dart';

class HTopDelegate extends SliverPersistentHeaderDelegate {
  @override
  double get maxExtent => 138;
  @override
  double get minExtent => 138;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    var tabTitles = [
      LocaleKeys.video.tr,
      if (ICache().isBusy) LocaleKeys.dress_up.tr,
      LocaleKeys.realistic.tr,
      LocaleKeys.anime.tr,
    ];

    var tabTypes = [
      ChaterListType.video,
      if (ICache().isBusy) ChaterListType.dressup,
      ChaterListType.realistic,
      ChaterListType.anime,
    ];

    var tabImages = [
      Assets.images.newUi.cateVideo.image(),
      if (ICache().isBusy) Assets.images.newUi.cateDressup.image(),
      Assets.images.newUi.cateReal.image(),
      Assets.images.newUi.cateAnime.image(),
    ];

    return Container(
      color: Colors.white,
      child: Stack(
        children: [
          CustomPaint(
            size: Size(MediaQuery.sizeOf(context).width, 138.0),
            painter: GradientPainter(),
          ),
          SizedBox(
            width: MediaQuery.sizeOf(context).width,
            height: 110,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: tabTypes.length,
              separatorBuilder: (context, index) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                final img = tabImages[index];
                final title = tabTitles[index];
                final type = tabTypes[index];
                return InkWell(
                  child: _buildCateItem(title: title, img: img),
                  onTap: () {
                    _onTapCate(title: title, type: type);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _onTapCate({required String title, required ChaterListType type}) {
    Get.to(() => ChaterListView(
          type: type,
          title: title,
        ));
  }

  Widget _buildCateItem({required String title, required Widget img}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Container(
        width: 110,
        height: 110,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
        ),
        child: Stack(
          children: [
            img,
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                padding: const EdgeInsets.only(top: 4, bottom: 12),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      Color(0xff524477),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Center(
                  child: Text(
                    title,
                    style: ITStyle.l3reg(
                      Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
