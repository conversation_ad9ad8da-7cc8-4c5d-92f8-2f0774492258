import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../other/i_enum.dart';
import 'home_list.dart';

class ChaterListView extends StatelessWidget {
  const ChaterListView({super.key, required this.title, required this.type});

  final String title;
  final ChaterListType type;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Assets.images.newUi.navBack.image(width: 24),
        ),
      ),
      body: HomeList(type: type),
    );
  }
}
