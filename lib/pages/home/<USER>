import 'package:bushy/service/event_api.dart';
import 'package:extended_image/extended_image.dart';
import 'package:get/get.dart';

import '../../common/i_dialog.dart';
import '../../data/chater.dart';
import '../../data/role_tag.dart';
import '../../other/event_util.dart';
import '../../other/i_cache.dart';
import '../../other/i_config.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/iap_util.dart';
import '../../other/info_helper.dart';
import '../../other/log_util.dart';
import '../../other/net_obs.dart';
import '../../other/user_helper.dart';
import '../../service/i_p.dart';

class HomeC extends GetxController {
  int curIndex = 0;

  // 标签
  List<RoleTagRes> roleTags = [];
  var selectTags = <RoleTag>{}.obs;
  Rx<Set<RoleTag>> filterEvent = Rx<Set<RoleTag>>({});

  // 关注
  Rx<(FollowEvent, String)> followEvent = (FollowEvent.follow, '').obs;

  @override
  void onInit() {
    super.onInit();

    if (NetObs.to.isConnected.value) {
      setupAndJump();
    } else {
      ever(NetObs.to.isConnected, (v) {
        if (v) {
          setupAndJump();
        }
      });
    }

    ever(UserHelper().localChanged, (_) {
      loadTags();
    });
  }

  Future<void> setupAndJump() async {
    await setup();
    jump();
  }

  Future<void> setup() async {
    try {
      await Future.wait([
        IConfig().requstBloom(),
        UserHelper().getUserInfo(),
        IAPUtil().queryProducts(),
      ]).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          return [];
        },
      );
      await InfoHeper().getIdfa();
      IP.updateEventParams();
      loadTags();
    } catch (e) {
      log.e('All tasks failed with error: $e');
    }

    update();
  }

  Future loadTags() async {
    final tags = await IP.roleTagsList();
    if (tags != null) {
      roleTags.assignAll(tags);
    }
  }

  void jump() {
    if (ICache().isBusy) {
      jumpForB();
    } else {
      jumpForA();
    }
  }

  void jumpForA() {
    recordInstallTime();
    ICache().isRA = true;
  }

  void jumpForB() async {
    final isShowDailyReward = await shouldShowDailyReward();
    final isVip = UserHelper().isVip.value;
    final isFirstLaunch = ICache().isRA == false;
    if (isFirstLaunch) {
      // 记录安装时间
      recordInstallTime();
      // 记录为重启
      ICache().isRA = true;

      EventApi().logInstallEvent();

      // 首次启动 获取指定人物聊天
      final startRole = await getSplashRole();
      if (startRole != null) {
        final roleId = startRole.id;
        IRouter.pushChat(roleId, showLoading: false);
      } else {
        jumpVip(isFirstLaunch);
      }
    } else {
      // 非首次启动 判断弹出奖励弹窗
      if (isShowDailyReward) {
        // 更新奖励时间戳
        ICache().lrd = DateTime.now().millisecondsSinceEpoch;
        IDialog.showLoginReward();
      } else {
        // 非vip用户 跳转订阅页
        if (!isVip) {
          jumpVip(isFirstLaunch);
        }
      }
    }
  }

  Future<void> recordInstallTime() async {
    ICache().itd = DateTime.now().millisecondsSinceEpoch;
  }

  Future<bool> shouldShowDailyReward() async {
    final installTimeMillis = ICache().itd;
    if (installTimeMillis <= 0) {
      // 记录安装时间
      recordInstallTime();
      return false; // 没有记录安装时间，不处理
    }

    final installTime = DateTime.fromMillisecondsSinceEpoch(installTimeMillis);
    final now = DateTime.now();

    // 安装后第一天不弹窗，只有从第二天开始才弹窗
    final isAfterSecondDay = now.year > installTime.year ||
        (now.year == installTime.year && now.month > installTime.month) ||
        (now.year == installTime.year && now.month == installTime.month && now.day > installTime.day);

    if (!isAfterSecondDay) {
      return false;
    }

    // 检查今天是否已经发过奖励（避免重复弹窗）
    final lastRewardDateMillis = ICache().lrd;
    if (lastRewardDateMillis > 0) {
      final lastRewardDate = DateTime.fromMillisecondsSinceEpoch(lastRewardDateMillis);

      // 如果今天已经发过奖励，则不弹窗
      if (now.year == lastRewardDate.year && now.month == lastRewardDate.month && now.day == lastRewardDate.day) {
        return false;
      }
    }

    return true; // 可以发奖励
  }

  // 获取开屏随机角色
  Future<Chater?> getSplashRole() async {
    UserHelper().getUserInfo();
    final role = await IP.splashRandomRole();
    final avatar = role?.avatar;
    if (avatar != null && avatar.isNotEmpty) {
      ExtendedNetworkImageProvider(avatar, cache: true);
    }
    return role;
  }

  void jumpVip(bool isFirstLaunch) async {
    IRouter.pushVip(ICache().isRA ? VipSource.relaunch : VipSource.launch);

    var event = ICache().isBusy ? 't_vipb' : 't_vipa';

    if (ICache().isRA) {
      event = '${event}_relaunch';
    } else {
      event = '${event}_launch';
      ICache().isRA = true;
    }
    logEvent(event);
  }
}
