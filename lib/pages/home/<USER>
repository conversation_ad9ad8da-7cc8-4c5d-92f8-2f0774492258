import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../common/follow_btn.dart';
import '../../common/s_button.dart';
import '../../common/s_image.dart';
import '../../data/chater.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_cache.dart';
import '../../other/i_fun.dart';
import '../../other/i_theme.dart';
import '../../other/svg_icon.dart';
import '../chat/photo_cell.dart';
import 'c_d_c.dart';

class ChaterDetail extends StatelessWidget {
  ChaterDetail({super.key});

  final ctr = Get.put(CDC());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: const Color(0xffF2F2F2),
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Assets.images.newUi.navBack.image(width: 24, color: Colors.white),
        ),
        actions: [
          Obx(() {
            ctr.isLoading.value;
            return FavoredButton(
              role: ctr.role,
              onCollect: (Chater role) {
                ctr.follow();
              },
            );
          }),
          const SizedBox(width: 16),
        ],
      ),
      body: Stack(
        children: [
          SImage(
            height: 300,
            width: double.infinity,
            url: ctr.role.avatar,
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 100),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: IColor.black8,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Assets.images.newUi.msg.image(width: 12),
                            const SizedBox(width: 4),
                            Text(
                              ctr.role.sessionCount ?? '',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                '${ctr.role.name}',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: ITStyle.l1sem(const Color(0xff333333)),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Text(
                                '${ctr.role.age} ${LocaleKeys.years_old.tr}',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: ITStyle.l6reg(const Color(0xff999999)),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        _buildTags(),
                        Container(color: IColor.n07, height: 1),
                        const SizedBox(height: 16),
                        _buildIntro(),
                        _buildImages(),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    LocaleKeys.option_title.tr,
                    style: ITStyle.l2sem(Colors.black),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 96,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => ctr.clearHistory(),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Row(
                                children: [
                                  SvgIcon(
                                    assetName: Assets.images.newUi.report,
                                    width: 12,
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      LocaleKeys.clear_history.tr,
                                      style: GoogleFonts.poppins(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xff727374),
                                      ),
                                    ),
                                  ),
                                  const Icon(
                                    Icons.chevron_right,
                                    color: Colors.black,
                                    weight: 1.0,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        Container(height: 1, color: const Color(0xffF2F2F2)),
                        Expanded(
                          child: InkWell(
                            onTap: () => report(),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Row(
                                children: [
                                  SvgIcon(
                                    assetName: Assets.images.newUi.report,
                                    width: 12,
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      LocaleKeys.report.tr,
                                      style: GoogleFonts.poppins(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xff727374),
                                      ),
                                    ),
                                  ),
                                  const Icon(
                                    Icons.chevron_right,
                                    color: Colors.black,
                                    weight: 1.0,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  SButton(
                    title: LocaleKeys.delete_chat.tr,
                    type: SButtonType.delete,
                    onTap: () => ctr.deleteChat(),
                    margin: const EdgeInsets.symmetric(horizontal: 49),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImages() {
    if (!ICache().isBusy || ctr.images.isEmpty) {
      return const SizedBox();
    }
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
          color: IColor.white1,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              LocaleKeys.enticing_pic.tr,
              style: ITStyle.l3sem(IColor.n02),
            ),
            const SizedBox(height: 12),
            GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                mainAxisSpacing: 4,
                crossAxisSpacing: 4,
                childAspectRatio: 1.0,
              ),
              itemBuilder: (BuildContext context, int index) {
                final image = ctr.images[index];
                final unlocked = image.unlocked ?? false;
                return PhotoCell(
                  image: image,
                  onTapPic: () => ctr.msgCtr.onTapImage(image),
                  onTap: () => ctr.msgCtr.onTapUnlockImage(image),
                  unlocked: unlocked,
                );
              },
              itemCount: ctr.images.length,
            ),
          ],
        ),
      );
    });
  }

  Container _buildIntro() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: IColor.white1,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            LocaleKeys.intro.tr,
            style: ITStyle.l3sem(IColor.n02),
          ),
          const SizedBox(height: 12),
          Text(
            ctr.role.aboutMe ?? '',
            style: ITStyle.l4reg(IColor.n02),
          ),
        ],
      ),
    );
  }

  Widget _buildTags() {
    if (!ICache().isBusy) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Wrap(
        spacing: 4,
        runSpacing: 4,
        children: ctr.role.tags
                ?.map(
                  (e) => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: IColor.black8,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      e,
                      style: ITStyle.l6reg(IColor.n10),
                    ),
                  ),
                )
                .toList() ??
            [],
      ),
    );
  }
}
