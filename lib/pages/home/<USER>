import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import '../../other/nav_obs.dart';
import '../../other/user_helper.dart';
import 'coin_btn.dart';
import 'filtter_page.dart';
import 'h_top_delegate.dart';
import 'home_c.dart';
import 'home_list.dart';
import 'phone_ctr.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with SingleTickerProviderStateMixin, RouteAware {
  final ctr = Get.put(HomeC());
  PhoneCtr callCtr = Get.put(PhoneCtr());

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    NavObs().routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didPopNext() {
    super.didPopNext();
    // 页面从其他页面返回时的逻辑处理
    if (ctr.curIndex == 0) {
      delayedCall();
    }
  }

  Future<void> delayedCall() async {
    print('-------->delayedCall: $callCtr');
    callCtr.callOut();
  }

  @override
  void dispose() {
    NavObs().routeObserver.unsubscribe(this);
    super.dispose();
  }

  void _onTapFiltter() async {
    if (ctr.roleTags.isEmpty) {
      // 如果为空，则加载标签
      await ctr.loadTags();
    }

    var tags = ctr.roleTags;

    // 检查标签是否为空，若为空则提示
    if (tags.isEmpty) {
      SmartDialog.showToast('No tags available');
      return;
    }

    Get.to(const FiltterPage());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: GetBuilder<HomeC>(
        builder: (_) {
          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverPersistentHeader(
                  delegate: HTopDelegate(),
                ),
              ];
            },
            body: _buildBody(),
          );
        },
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      titleSpacing: 16, // 默认的 title 间距
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: IColor.brandjb,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: Row(
        children: [
          InkWell(
            onTap: () {
              IRouter.pushSetting();
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
              // height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xff906bf7).withOpacity(0.2),
              ),
              child: Row(
                children: [
                  Assets.images.newUi.setting.image(width: 16),
                  const SizedBox(width: 4),
                  Text(
                    LocaleKeys.settings.tr,
                    style: ITStyle.l5sem(IColor.n02),
                  ),
                ],
              ),
            ),
          ),
          const Spacer(),
          Obx(() {
            return UserHelper().isVip.value
                ? const SizedBox()
                : GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      IRouter.pushVip(VipSource.homevip);
                    },
                    child: Assets.images.newUi.vip.image(width: 24));
          }),
          const SizedBox(width: 8),
          const CoinBtn(from: ConsumeSource.home),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                LocaleKeys.recommended_for_you.tr,
                style: ITStyle.l3sem(IColor.n01),
              ),
              const Spacer(),
              InkWell(
                onTap: () {
                  IRouter.pushSearch();
                },
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Assets.images.newUi.search.image(
                    width: 24,
                    height: 24,
                  ),
                ),
              ),
              if (ICache().isBusy)
                Container(
                  color: IColor.n06,
                  width: 1,
                  height: 8,
                ),
              if (ICache().isBusy)
                InkWell(
                  onTap: _onTapFiltter,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Assets.images.newUi.filtter.image(
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        const Expanded(
          child: HomeList(type: ChaterListType.favored),
        ),
      ],
    );
  }
}
