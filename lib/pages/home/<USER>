import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

import '../../common/i_empty.dart';
import '../../data/chater.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../other/i_theme.dart';
import 'chater_cell.dart';
import 'r_s_c.dart';

class SearchV extends StatefulWidget {
  const SearchV({super.key});

  @override
  State<SearchV> createState() => _SearchVState();
}

class _SearchVState extends State<SearchV> {
  final focusNode = FocusNode();
  final textController = TextEditingController();

  final ctr = Get.put<RSC>(RSC());

  @override
  void initState() {
    super.initState();
    focusNode.requestFocus();
  }

  @override
  void dispose() {
    focusNode.unfocus();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.search.tr),
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Assets.images.newUi.navBack.image(width: 24),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(32),
          child: _buildTextField(),
        ),
      ),
      body: Obx(() {
        final list = ctr.list;
        final type = ctr.type.value;

        if (type != null) {
          return const IEmpty(type: EmptyType.search);
        }
        return Padding(
          padding: const EdgeInsets.only(top: 16),
          child: MasonryGridView.count(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            crossAxisCount: 2,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            itemCount: list.length,
            itemBuilder: (context, index) {
              final role = list[index];
              return SizedBox(
                height: index.isOdd ? 280 : 240,
                child: ChaterCell(
                  role: role,
                  onCollect: (Chater role) {
                    ctr.onCollect(index);
                  },
                  type: ChaterListType.favored,
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildTextField() {
    return Container(
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        color: IColor.n09,
      ),
      child: Row(
        children: [
          Assets.images.newUi.inputSearch.image(width: 24, height: 24, color: IColor.n05),
          Expanded(
            child: Center(
              child: TextField(
                onChanged: (query) {
                  // 更新 searchQuery
                  ctr.searchQuery.value = query;
                },
                autofocus: false,
                cursorColor: IColor.brand,
                textInputAction: TextInputAction.done,
                onEditingComplete: () {
                  ctr.searchQuery.value = textController.text;
                },
                minLines: 1,
                maxLength: 20,
                style: const TextStyle(
                  height: 1,
                  color: IColor.brand,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                controller: textController,
                decoration: InputDecoration(
                  hintText: LocaleKeys.type_a_name_to_find_bushys.tr,
                  counterText: '', // 去掉字数显示
                  hintStyle: ITStyle.l5reg(const Color(0xffcccccc)),
                  fillColor: Colors.transparent,
                  border: InputBorder.none,
                  filled: true,
                  isDense: true,
                ),
                focusNode: focusNode,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
