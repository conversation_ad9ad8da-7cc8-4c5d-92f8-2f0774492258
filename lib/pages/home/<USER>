import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../gen/assets.gen.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import '../../other/user_helper.dart';

class CoinBtn extends StatelessWidget {
  const CoinBtn({
    super.key,
    required this.from,
  });

  final ConsumeSource from;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final balance = UserHelper().balance.value;
      final text = '$balance';
      return InkWell(
        onTap: () {
          IRouter.pushGem(from);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: from == ConsumeSource.chat ? const Color(0xff906BF7) : const Color(0xff906bf7).withOpacity(0.2),
          ),
          child: Row(
            children: [
              Assets.images.newUi.gold.image(width: 16),
              const SizedBox(width: 4),
              Text(
                text,
                style: ITStyle.l5sem(from == ConsumeSource.chat ? Colors.white : Colors.black),
              ),
            ],
          ),
        ),
      );
    });
  }
}
