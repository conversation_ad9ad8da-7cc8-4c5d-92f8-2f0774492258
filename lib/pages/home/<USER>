import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../data/role_tag.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_theme.dart';
import 'home_c.dart';

class FiltterPage extends StatefulWidget {
  const FiltterPage({super.key});

  @override
  State<FiltterPage> createState() => _FiltterPageState();
}

class _FiltterPageState extends State<FiltterPage> {
  final ctr = Get.find<HomeC>();

  RoleTagRes? selectedType;

  @override
  void initState() {
    super.initState();

    selectedType = ctr.roleTags.firstOrNull;
  }

  @override
  Widget build(BuildContext context) {
    final tags = selectedType?.tags;

    bool containsAll = false;
    if (tags != null && tags.isNotEmpty) {
      containsAll = ctr.selectTags.containsAll(tags);
    }

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Assets.images.newUi.navBack.image(width: 24),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocaleKeys.choose_your_tags.tr,
              style: ITStyle.l2sem(IColor.n02),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                _buildType(),
                const Spacer(),
                InkWell(
                  splashColor: Colors.transparent, // 去除水波纹
                  highlightColor: Colors.transparent, // 去除点击高亮
                  onTap: () {
                    if (containsAll) {
                      ctr.selectTags.removeAll(tags ?? []);
                    } else {
                      ctr.selectTags.addAll(tags ?? []);
                    }
                    setState(() {});
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Text(
                          containsAll ? LocaleKeys.unselect_all.tr : LocaleKeys.select_all.tr,
                          style: ITStyle.l5reg(IColor.brand),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Expanded(
              child: _buildTags(),
            ),
            const SizedBox(height: 32),
            Container(
              height: 32,
              margin: const EdgeInsets.symmetric(horizontal: 60),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: IColor.brand,
              ),
              child: InkWell(
                splashColor: Colors.transparent, // 去除水波纹
                highlightColor: Colors.transparent, // 去除点击高亮
                onTap: () {
                  Get.back();
                  ctr.filterEvent.value = Set<RoleTag>.from(ctr.selectTags);
                  ctr.filterEvent.refresh();
                },
                child: Center(
                  child: Text(
                    LocaleKeys.confirm.tr,
                    style: ITStyle.l4sem(IColor.n10),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildTags() {
    final tags = selectedType?.tags;
    if (tags == null || tags.isEmpty) {
      return const SizedBox();
    }

    return SingleChildScrollView(
      child: SizedBox(
        width: double.infinity,
        child: Wrap(
          spacing: 8,
          runSpacing: 8,
          direction: Axis.horizontal, // 显示为水平布局
          alignment: WrapAlignment.start,
          children: tags.map((e) {
            var isSelected = ctr.selectTags.contains(e);

            return InkWell(
              splashColor: Colors.transparent, // 去除水波纹
              highlightColor: Colors.transparent, // 去除点击高亮
              onTap: () {
                if (ctr.selectTags.contains(e)) {
                  ctr.selectTags.remove(e);
                } else {
                  ctr.selectTags.add(e);
                }
                setState(() {});
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildItem(isSelected, e),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildItem(bool isSelected, RoleTag e) {
    return Container(
      height: 32,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isSelected ? IColor.brand1 : IColor.n08,
        borderRadius: const BorderRadius.all(Radius.circular(16)),
      ),
      alignment: Alignment.center,
      child: Text(
        e.name ?? '',
        style: isSelected ? ITStyle.l4sem(IColor.n02) : ITStyle.l4sem(IColor.n03),
      ),
    );
  }

  Widget _buildType() {
    var tags = ctr.roleTags;
    List<RoleTagRes> result = (tags.length > 2) ? tags.take(2).toList() : tags;

    RoleTagRes type1 = result[0];

    RoleTagRes? type2;
    if (result.length > 1) {
      type2 = result[1];
    }

    return Container(
      height: 64,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: IColor.n08,
          width: 1.0,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            splashColor: Colors.transparent, // 去除水波纹
            highlightColor: Colors.transparent, // 去除点击高亮
            onTap: () {
              selectedType = type1;
              setState(() {});
            },
            child: _buildTypeItem1(type1),
          ),
          if (type2 != null)
            InkWell(
              splashColor: Colors.transparent, // 去除水波纹
              highlightColor: Colors.transparent, // 去除点击高亮
              onTap: () {
                selectedType = type2;
                setState(() {});
              },
              child: _buildTypeItem1(type2),
            ),
        ],
      ),
    );
  }

  Widget _buildTypeItem1(RoleTagRes type) {
    bool isSelected = type == selectedType;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            type.labelType ?? '',
            style: ITStyle.l5sem(IColor.n02),
          ),
          const SizedBox(height: 4),
          isSelected ? Assets.images.newUi.check.image(width: 20) : Assets.images.newUi.checkNo.image(width: 20),
        ],
      ),
    );
  }
}
