import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../common/i_dialog.dart';
import '../../data/chater.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../service/i_p.dart';
import '../chat/msg_ctr.dart';
import 'home_c.dart';

class CDC extends GetxController {
  var isLoading = false.obs;
  late Chater role;
  final msgCtr = Get.find<MsgCtr>();
  var images = <RoleImage>[].obs;

  @override
  void onInit() {
    super.onInit();

    final arguments = Get.arguments;
    if (arguments != null && arguments is Chater) {
      role = arguments;
    }

    images.value = msgCtr.role.images ?? [];

    ever(msgCtr.roleImagesChaned, (_) {
      images.value = msgCtr.role.images ?? [];
    });
  }

  void deleteChat() async {
    IDialog.alert(
      message: LocaleKeys.delete_chat_confirmation.tr,
      cancelText: LocaleKeys.cancel.tr,
      onConfirm: () async {
        SmartDialog.dismiss();
        var res = await msgCtr.deleteConv();
        if (res) {
          Get.back();
          Get.back();
        }
      },
    );
  }

  void clearHistory() async {
    IDialog.alert(
      message: LocaleKeys.clear_history_confirmation.tr,
      cancelText: LocaleKeys.cancel.tr,
      onConfirm: () async {
        SmartDialog.dismiss();
        var res = await msgCtr.resetConv();
        if (res) {
          SmartDialog.showToast(LocaleKeys.clear_history_success.tr);
        } else {
          SmartDialog.showToast(LocaleKeys.clear_history_failed.tr);
        }
      },
    );
  }

  Future follow() async {
    final id = role.id;
    if (id == null) {
      return;
    }
    if (isLoading.value) {
      return;
    }
    isLoading.value = true;

    if (role.collect == true) {
      final res = await IP.cancelCollectRole(id);
      if (res) {
        role.collect = false;
        Get.find<HomeC>().followEvent.value = (FollowEvent.unfollow, id);
      }
      isLoading.value = false;
    } else {
      final res = await IP.collectRole(id);
      if (res) {
        role.collect = true;
        Get.find<HomeC>().followEvent.value = (FollowEvent.follow, id);
      }
      isLoading.value = false;

      if (!IDialog.rateCollectShowd) {
        IDialog.showRateUs(LocaleKeys.help_rate_message.tr);
        IDialog.rateCollectShowd = true;
      }
    }
  }
}
