import 'dart:ui';

import 'package:flutter/material.dart';

import '../../common/follow_btn.dart';
import '../../common/s_image.dart';
import '../../data/chater.dart';
import '../../gen/assets.gen.dart';
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';

const kNSFW = 'NSFW';
const kBDSM = 'BDSM';

class ChaterCell extends StatelessWidget {
  const ChaterCell({
    super.key,
    required this.role,
    required this.onCollect,
    required this.type,
  });

  final Chater role;
  final void Function(Chater role) onCollect;
  final ChaterListType type;

  void _onTap() {
    FocusManager.instance.primaryFocus?.unfocus();

    final id = role.id;
    if (id == null) {
      return;
    }

    if (type == ChaterListType.video) {
      IRouter.pushPhoneGuide(role: role);
      return;
    }

    IRouter.pushChat(id);
  }

  @override
  Widget build(BuildContext context) {
    final tags = role.tags;
    List<String> result = (tags != null && tags.length > 3) ? tags.take(3).toList() : tags ?? [];
    if ((role.tagType?.contains(kNSFW) ?? false) && !result.contains(kNSFW)) {
      result.insert(0, kNSFW);
    }
    if ((role.tagType?.contains(kBDSM) ?? false) && !result.contains(kBDSM)) {
      result.insert(0, kBDSM);
    }

    return InkWell(
      onTap: _onTap,
      borderRadius: BorderRadius.circular(16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            Positioned.fill(
              child: SImage(
                url: role.avatar,
                borderRadius: BorderRadius.circular(16),
                border: role.vip ?? false
                    ? Border.all(
                        color: IColor.brand,
                        width: 2,
                      )
                    : null,
              ),
            ),
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.all(2.0),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        const Color(0xff101010).withOpacity(0.8),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: const [0.5, 1.0],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (ICache().isBusy)
                    Row(
                      children: [
                        _buildIconButton(
                          Assets.images.newUi.tagVideo.image(width: 12, height: 12),
                          role.genVideo,
                        ),
                        _buildIconButton(
                          Assets.images.newUi.tagImg.image(width: 12, height: 12),
                          role.genPhoto,
                        ),
                        _buildIconButton(
                          Assets.images.newUi.tagClothe.image(width: 12, height: 12),
                          role.changeClothing,
                        ),
                      ],
                    ),
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          role.name ?? '',
                          style: ITStyle.l3sem(IColor.n10),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Colors.white.withOpacity(0.2),
                        ),
                        child: Text(
                          '${role.age ?? 0}',
                          style: ITStyle.l6reg(IColor.n10),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  if (result.isNotEmpty && ICache().isBusy) _buildTags(result),
                  const SizedBox(height: 8),
                ],
              ),
            ),
            PositionedDirectional(
              top: 8,
              end: 8,
              child: FavoredButton(onCollect: onCollect, role: role),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTags(List<String> result) {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: List.generate(result.length, (index) {
        final text = result[index];
        var bgColor = Colors.white.withOpacity(0.2);
        var textColor = Colors.white;
        var isGradient = false;
        if (text == kNSFW) {
          isGradient = true;
          textColor = IColor.n02;
        } else if (text == kBDSM) {
          bgColor = IColor.n02.withOpacity(0.2);
          textColor = IColor.brand;
        }
        return ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4), // 模糊效果
            child: Container(
              decoration: BoxDecoration(
                color: isGradient ? null : bgColor,
                gradient: isGradient
                    ? const LinearGradient(
                        colors: IColor.brandjb,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              child: Text(
                text,
                style: ITStyle.l6reg(textColor),
              ),
            ),
          ),
        );
      }),
    );
  }
}

Widget _buildIconButton(Widget icon, bool? show) {
  if (!(show ?? false)) return const SizedBox();

  return ClipRRect(
    borderRadius: BorderRadius.circular(12),
    child: BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
      child: Container(
        height: 24,
        width: 24,
        margin: const EdgeInsetsDirectional.only(end: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white.withOpacity(0.2),
        ),
        child: Center(child: icon),
      ),
    ),
  );
}
