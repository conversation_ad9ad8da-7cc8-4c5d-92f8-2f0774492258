import 'package:bushy/other/user_helper.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../common/i_empty.dart';
import '../../data/chater.dart';
import '../../data/moment.dart';
import '../../data/session.dart';
import '../../other/i_router.dart';
import '../../other/net_obs.dart';
import '../../service/i_p.dart';

class MoC extends GetxController {
  final EasyRefreshController rfctr = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  int page = 1;
  int size = 1000;
  List<Moments> list = [];
  EmptyType? type = EmptyType.loading;
  bool isNoMoreData = false;

  @override
  void onInit() {
    super.onInit();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      rfctr.callRefresh();
    });

    ever(UserHelper().localChanged, (_) {
      onRefresh();
    });
  }

  @override
  void onClose() {
    rfctr.dispose();
    super.onClose();
  }

  Future<void> onRefresh() async {
    page = 1;
    await _fetchData();
    rfctr.finishRefresh();
    rfctr.resetFooter();
  }

  Future<void> onLoad() async {
    page++;
    await _fetchData();
    rfctr.finishLoad(isNoMoreData ? IndicatorResult.noMore : IndicatorResult.none);
  }

  Future<MomentsRes?> _fetchData() async {
    try {
      final res = await IP.momensListPage(page: page, size: size);
      if (res == null || (res.records?.isEmpty ?? true)) {
        type = list.isEmpty ? (NetObs().isConnected.value == false ? EmptyType.noNetwork : EmptyType.empty) : type;
        return null;
      }
      isNoMoreData = (res.records?.length ?? 0) < size;

      if (page == 1) {
        list.clear();
      }

      type = null;
      list.addAll(res.records!);
      update();
      return res;
    } catch (e) {
      type = list.isEmpty ? (NetObs().isConnected.value == false ? EmptyType.noNetwork : EmptyType.empty) : type;
      if (page > 1) page--;
      update();
      return null;
    }
  }

  void onItemClick(Moments data) async {
    final chaterId = data.characterId;
    if (chaterId == null) {
      SmartDialog.showToast('No chaterId');
      return;
    }

    SmartDialog.showLoading();

    try {
      // 并行执行异步任务
      final roleFuture = IP.loadRoleById(chaterId);
      final sessionFuture = IP.addSession(chaterId);
      final results = await Future.wait([roleFuture, sessionFuture]);

      final Chater? role = results[0] as Chater?;
      final Session? session = results[1] as Session?;

      SmartDialog.dismiss();

      if (role == null) {
        SmartDialog.showToast('No role');
        return;
      }
      if (session == null) {
        SmartDialog.showToast('No session');
        return;
      }

      IRouter.pushChat(role.id);
    } catch (e) {
      // 捕获异常并提示用户
      SmartDialog.dismiss();
      SmartDialog.showToast('Failed to load data: $e');
    }
  }

  void onPlay(Moments data) {
    var isVideo = data.cover != null && data.duration != null;
    var imgUrl = isVideo ? data.cover : data.media;

    if (isVideo) {
      if (data.media != null) {
        IRouter.pushVideoPreview(data.media!);
      } else {
        SmartDialog.showToast('No video');
      }
    } else {
      IRouter.pushImagePreview(imgUrl ?? '');
    }
  }
}
