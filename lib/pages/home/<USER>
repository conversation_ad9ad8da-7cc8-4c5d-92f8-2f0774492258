import 'dart:ui';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../common/i_empty.dart';
import '../../common/s_image.dart';
import '../../data/moment.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../other/i_ext.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import '../../other/svg_icon.dart';
import '../../other/user_helper.dart';
import 'mo_c.dart';

class MomentsV extends StatelessWidget {
  MomentsV({super.key});

  final ctr = Get.put(MoC());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 16,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        title: Text(
          LocaleKeys.moments.tr,
          style: ITStyle.l1sem(IColor.n02),
        ),
        centerTitle: false,
      ),
      body: SafeArea(
        child: GetBuilder<MoC>(
          builder: (_) {
            return EasyRefresh.builder(
              controller: ctr.rfctr,
              onRefresh: ctr.onRefresh,
              onLoad: ctr.onLoad,
              childBuilder: (context, physics) {
                final list = ctr.list;
                final type = ctr.type;
                if (list.isEmpty && type != null) {
                  return IEmpty(type: type, physics: physics);
                }

                return ListView.separated(
                  physics: physics,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemBuilder: (BuildContext context, int index) {
                    final data = ctr.list[index];
                    var isVideo = data.cover != null && data.duration != null;
                    var imgUrl = isVideo ? data.cover : data.media;
                    var istop = data.istop ?? false;

                    return Container(
                      decoration: BoxDecoration(
                        color: IColor.white1,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildName(data, isVideo),
                          const SizedBox(height: 16),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (!isVideo)
                                Container(
                                  width: 48,
                                  height: 48,
                                  margin: const EdgeInsetsDirectional.only(end: 12),
                                  decoration: const BoxDecoration(
                                    color: IColor.n02,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Assets.images.newUi.tagImg.image(
                                      width: 24,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              Expanded(
                                child: SizedBox(
                                  height: 240,
                                  child: Stack(
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          ctr.onPlay(data);
                                        },
                                        child: SImage(
                                          height: 240,
                                          width: double.infinity,
                                          url: imgUrl,
                                          borderRadius: BorderRadius.circular(16),
                                        ),
                                      ),
                                      Positioned.fill(
                                        child: _buildLock(istop, isVideo, data),
                                      ),
                                      PositionedDirectional(
                                        start: isVideo ? null : 0,
                                        end: isVideo ? 0 : null,
                                        bottom: 0,
                                        child: Container(
                                          constraints: BoxConstraints(
                                            maxWidth: Get.width - 128,
                                          ),
                                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                          decoration: BoxDecoration(
                                            color: IColor.n10,
                                            borderRadius: isVideo
                                                ? const BorderRadiusDirectional.only(
                                                    topStart: Radius.circular(16),
                                                  )
                                                : const BorderRadiusDirectional.only(
                                                    topEnd: Radius.circular(16),
                                                  ),
                                          ),
                                          child: Text(
                                            data.text ?? '',
                                            style: ITStyle.l5reg(IColor.n02),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (isVideo)
                                Container(
                                  width: 48,
                                  height: 48,
                                  margin: const EdgeInsetsDirectional.only(start: 12),
                                  decoration: const BoxDecoration(
                                    color: IColor.n02,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Assets.images.newUi.tagVideo.image(
                                      width: 24,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox(height: 16);
                  },
                  itemCount: ctr.list.length,
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildLock(bool istop, bool isVideo, Moments data) {
    Widget widget = _buildLockView(isVideo, data);

    Widget play = _buildPlayButton(data);

    return Obx(() {
      var isVip = UserHelper().isVip.value;
      if (isVip || istop) {
        return isVideo ? play : const SizedBox();
      } else {
        return widget;
      }
    });
  }

  Widget _buildLockView(bool isVideo, Moments data) {
    return GestureDetector(
      onTap: () {
        IRouter.pushVip(isVideo ? VipSource.postvideo : VipSource.postpic);
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 64, sigmaY: 64),
          child: Container(
            color: IColor.black5,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Stack(
                children: [
                  if (isVideo)
                    Container(
                      height: 16,
                      margin: const EdgeInsets.only(top: 12),
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        color: IColor.white1,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        formatVideoDuration(data.duration ?? 0),
                        style: ITStyle.l6reg(IColor.n02),
                      ),
                    ),
                  Center(child: _buildLoackButton()),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayButton(Moments data) {
    return Center(
      child: InkWell(
        onTap: () {
          ctr.onPlay(data);
        },
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(16),
          ),
          child: SvgIcon(assetName: Assets.images.play, color: Colors.white),
        ),
      ),
    );
  }

  Column _buildLoackButton() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Container(
            width: 32,
            height: 32,
            color: IColor.white2,
            child: Center(
              child: Assets.images.newUi.lock.image(width: 16, height: 16),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 24,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12)),
                color: IColor.brand,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.subscribe_to_view_posts.tr,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: ITStyle.l6sem(IColor.n10),
                  ),
                ],
              ),
            ),
          ],
        )
      ],
    );
  }
}

void _toChat(Moments data) {
  IRouter.pushChat(data.characterId);
}

Widget _buildName(Moments data, bool isVideo) {
  return Row(
    children: [
      InkWell(
        splashColor: Colors.transparent, // 取消水波纹效果
        highlightColor: Colors.transparent, // 取消高亮效果
        onTap: () => _toChat(data),
        child: Stack(
          alignment: AlignmentDirectional.topEnd,
          children: [
            SImage(
              url: data.characterAvatar,
              shape: BoxShape.circle,
              width: 32,
              height: 32,
            ),
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: IColor.green,
                border: Border.all(color: IColor.n10, width: 1),
              ),
            )
          ],
        ),
      ),
      const SizedBox(width: 4),
      Expanded(
        child: InkWell(
          splashColor: Colors.transparent, // 取消水波纹效果
          highlightColor: Colors.transparent, // 取消高亮效果
          onTap: () => _toChat(data),
          child: Text(
            data.characterName ?? '',
            style: ITStyle.l4sem(const Color(0xff333333)),
          ),
        ),
      ),
    ],
  );
}
