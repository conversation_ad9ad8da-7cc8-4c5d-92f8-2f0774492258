import 'package:bushy/other/i_ext.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../data/chater.dart';
import '../../other/event_util.dart';
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import '../../other/i_path.dart';
import '../../other/i_router.dart';
import '../../other/log_util.dart';
import '../../other/nav_obs.dart';
import '../../other/user_helper.dart';
import '../../pages/home/<USER>';
import '../../pages/mian/main_v.dart';

class PhoneCtr extends GetxController {
  // 主动来电
  final List<Chater> _callList = [];
  Chater? _callRole;
  int _callCount = 0;
  int _lastCallTime = 0;
  bool _calling = false;

  void onCall(List<Chater>? list) async {
    try {
      if (list == null || list.isEmpty) return;
      _callList.assignAll(list);
      final role =
          list.where((element) => element.gender == 1 && element.renderStyle == 'REALISTIC').toList().randomOrNull;
      if (role == null) {
        return;
      }
      _callRole = role;
    } catch (e) {
      log.e(e.toString());
    }
  }

  Future callOut() async {
    try {
      if (!canCall() || _calling) {
        return;
      }
      if (_callRole == null) {
        return;
      }

      String? url;
      if (_callRole!.videoChat == true) {
        logEvent('t_ai_videocall');
        final guide = _callRole?.characterVideoChat?.firstWhereOrNull((e) => e.tag == 'guide');
        url = guide?.gifUrl;
      } else {
        logEvent('t_ai_audiocall');
        url = _callRole?.avatar;
      }

      if (url == null || url.isEmpty) {
        return;
      }

      if (!canCall() || _calling) {
        return;
      }

      final roleId = _callRole?.id;
      if (roleId == null || roleId.isEmpty) {
        return;
      }

      await Future.delayed(const Duration(seconds: 4));

      if (!canCall() || _calling) {
        return;
      }

      _calling = true;

      _lastCallTime = DateTime.now().millisecondsSinceEpoch;
      _callCount++;

      const sessionId = 0;

      IRouter.pushPhone(
        sessionId: sessionId,
        role: _callRole!,
        showVideo: true,
        callState: CallState.incoming,
      );

      final role = _callList
          .where((element) => element.gender == 1 && element.renderStyle == 'REALISTIC' && element.id != _callRole?.id)
          .toList()
          .randomOrNull;
      if (role == null) {
        return;
      }
      _callRole = role;
    } catch (e) {
      log.e(e.toString());
    } finally {
      _calling = false;
    }
  }

  bool canCall() {
    if (!ICache().isBusy) {
      log.d('-------->canCall: false isA');
      return false;
    }

    if (rootIndex != RootIndex.home) {
      log.d('-------->canCall: false rootIndex != 0, rootIndex = $rootIndex');
      return false;
    }

    if (Get.find<HomeC>().curIndex != 0) {
      log.d('-------->canCall: HomeController.curIndex != 0, rootIndex = $rootIndex');
      return false;
    }

    if (UserHelper().isVip.value) {
      log.d('-------->canCall: false isVip');
      return false;
    }
    if (_callCount > 2) {
      log.d('-------->canCall:false  _callCount > 2');
      return false;
    }
    if (SmartDialog.checkExist(tag: DialogTag.sigin.name)) {
      log.d('-------->canCall: false SmartDialog.show.loginReward');
      return false;
    }
    int currentTimestamp = DateTime.now().millisecondsSinceEpoch;
    if (_lastCallTime > 0 && currentTimestamp - _lastCallTime < 2 * 60 * 1000) {
      log.d('-------->canCall: 180s false');
      return false;
    }

    if (NavObs().curRoute?.settings.name != IPath.root) {
      log.d('-------->canCall: false curRoute is not root');
      return false;
    }
    return true;
  }
}
