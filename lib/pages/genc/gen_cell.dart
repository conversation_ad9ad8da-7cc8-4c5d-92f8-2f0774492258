import 'package:flutter/material.dart';

import '../../common/s_image.dart';
import '../../data/chater.dart';
import '../../gen/assets.gen.dart';
import '../../other/i_theme.dart';

class GenCell extends StatelessWidget {
  const GenCell({
    super.key,
    required this.role,
    required this.isSelected,
    required this.onTapRole,
  });

  final Chater role;
  final bool isSelected;
  final void Function(Chater role) onTapRole;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent, // 去除水波纹
      highlightColor: Colors.transparent, // 去除点击高亮
      borderRadius: BorderRadius.circular(16),
      onTap: () {
        onTapRole(role);
      },
      child: Column(
        children: [
          Stack(
            alignment: AlignmentDirectional.bottomEnd,
            children: [
              SImage(
                width: 111,
                height: 111,
                url: role.avatar,
                borderRadius: BorderRadius.circular(16),
                border: isSelected ? Border.all(color: IColor.brand, width: 2) : null,
              ),
              if (isSelected)
                Container(
                  width: 34,
                  height: 24,
                  decoration: const BoxDecoration(
                    color: IColor.brand,
                    borderRadius: BorderRadiusDirectional.only(
                      topStart: Radius.circular(16),
                      bottomEnd: Radius.circular(16),
                    ),
                  ),
                  child: Center(child: Assets.images.newUi.choose.image(width: 10)),
                )
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${role.name}',
            textAlign: TextAlign.start,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: ITStyle.l6sem(IColor.n02),
          ),
        ],
      ),
    );
  }
}
