import 'package:bushy/other/user_helper.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:get/get.dart';

import '../../common/i_empty.dart';
import '../../data/chater.dart';
import '../../other/i_enum.dart';
import '../../other/net_obs.dart';
import '../../service/i_p.dart';

class GenCtr {
  GenCtr(this.ctype) {
    ever(UserHelper().localChanged, (_) {
      onRefresh();
    });
  }
  final CreateType ctype;

  int page = 1;
  int size = 10;
  var list = <Chater>[].obs;

  EmptyType? type = EmptyType.loading;
  bool isNoMoreData = false;

  Chater? selectedRole;

  late final EasyRefreshController refreshCtr = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  Future<void> onRefresh() async {
    page = 1;
    await _fetchData();
    refreshCtr.finishRefresh();
    refreshCtr.resetFooter();
  }

  Future<void> onLoad() async {
    await _fetchData();
    refreshCtr.finishLoad(isNoMoreData ? IndicatorResult.noMore : IndicatorResult.none);
  }

  Future<void> _fetchData() async {
    try {
      final res = await IP.homeList(
        page: page,
        size: size,
        genImg: ctype == CreateType.photo,
        genVideo: ctype == CreateType.video,
      );

      isNoMoreData = (res?.records?.length ?? 0) < size;

      if (page == 1) {
        list.assignAll(res?.records ?? []);
        selectedRole = list.firstOrNull;
      } else {
        list.addAll(res?.records ?? []);
      }

      page++;

      type = list.isEmpty ? EmptyType.empty : null;
    } catch (e) {
      type = list.isEmpty
          ? (NetObs.to.isConnected.value == false ? EmptyType.noNetwork : EmptyType.empty) //
          : type;
    }
  }
}
