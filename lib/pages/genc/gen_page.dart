import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/gradient_painter.dart';
import '../../common/keep_alive_wrapper.dart';
import '../../common/tab_page_view.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../other/i_theme.dart';
import 'gen_list.dart';

class GenPage extends StatefulWidget {
  const GenPage({super.key});

  @override
  State<GenPage> createState() => _GenPageState();
}

class _GenPageState extends State<GenPage> {
  int curIndex = 0;

  List<String> tabTitles = [];

  List<Widget> tabContents = [];

  @override
  void initState() {
    super.initState();

    tabContents = [
      const KeepAliveWrapper(child: GenList(type: CreateType.photo)),
      const KeepAliveWrapper(child: GenList(type: CreateType.video)),
    ];
  }

  void _onPageChanged(index) {
    curIndex = index;
  }

  @override
  Widget build(BuildContext context) {
    tabTitles = [
      LocaleKeys.create_image.tr,
      LocaleKeys.create_video.tr,
    ];
    return Scaffold(
      body: Stack(
        children: [
          CustomPaint(
            size: Size(MediaQuery.sizeOf(context).width, 120.0),
            painter: GradientPainter(),
          ),
          SafeArea(
            child: TabPageView(
              tabTitles: tabTitles,
              tabContents: tabContents,
              tabHeight: 32,
              animatedPageSwitch: true,
              tabBarAlignment: MainAxisAlignment.start,
              separatorBuilder: (context, index) => const SizedBox(width: 12),
              tabBarPadding: const EdgeInsets.symmetric(horizontal: 16),
              onPageChanged: _onPageChanged,
              itemBuilder: (BuildContext context, int index, bool isSelected) {
                return Center(
                  child: _buildButton(text: tabTitles[index], isSelected: isSelected),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required bool isSelected,
  }) {
    return Container(
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isSelected ? IColor.brand : Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          text,
          style: isSelected ? ITStyle.l5sem(IColor.n10) : ITStyle.l5reg(IColor.n02),
        ),
      ),
    );
  }
}
