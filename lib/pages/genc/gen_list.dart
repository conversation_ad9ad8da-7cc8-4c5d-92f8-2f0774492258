import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../common/s_button.dart';
import '../../data/chater.dart';
import '../../generated/locales.g.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import 'gen_cell.dart';
import 'gen_ctr.dart';

class GenList extends StatefulWidget {
  const GenList({super.key, required this.type});

  final CreateType type;

  @override
  State<GenList> createState() => _GenListState();
}

class _GenListState extends State<GenList> {
  late GenCtr ctr;

  @override
  void initState() {
    super.initState();

    ctr = GenCtr(widget.type);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ctr.refreshCtr.callRefresh();
    });
  }

  void onTapRole(Chater? role) {
    if (role == null) {
      SmartDialog.showToast(LocaleKeys.choose_character.tr);
      return;
    }
    IRouter.pushCreate(role: role, type: widget.type);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 40),
          Text(
            LocaleKeys.choose_character.tr,
            style: ITStyle.l3sem(const Color(0xff333333)),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SizedBox(
              height: 32,
              child: EasyRefresh(
                controller: ctr.refreshCtr,
                onLoad: ctr.onLoad,
                onRefresh: ctr.onRefresh,
                child: Obx(() {
                  final list = ctr.list;
                  return GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      mainAxisSpacing: 8,
                      crossAxisSpacing: 8,
                      childAspectRatio: 111.0 / 140.0,
                    ),
                    itemBuilder: (BuildContext context, int index) {
                      final role = list[index];
                      return GenCell(
                        role: role,
                        isSelected: ctr.selectedRole?.id == role.id,
                        onTapRole: (role) {
                          ctr.selectedRole = role;
                          setState(() {});
                        },
                      );
                    },
                    itemCount: list.length,
                  );
                }),
              ),
            ),
          ),
          const SizedBox(height: 24),
          SButton(
            title: LocaleKeys.continue_txt.tr,
            type: SButtonType.primary,
            onTap: () {
              onTapRole(ctr.selectedRole);
            },
          ),
          const SizedBox(height: 70),
        ],
      ),
    );
  }
}
