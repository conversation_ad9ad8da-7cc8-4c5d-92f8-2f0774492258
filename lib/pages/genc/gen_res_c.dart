import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../data/chater.dart';
import '../../data/gen_res.dart';
import '../../generated/locales.g.dart';
import '../../other/event_util.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/user_helper.dart';
import '../../service/i_p.dart';

class GenResC extends GetxController {
  late Chater role;
  late CreateType type;

  var selectedEffect = ''.obs;
  var effectList = <String>[].obs;

  GenRes? data0;
  var isLoading = false.obs;
  late int generatTime;
  late String generatText;

  // 保存已生成过的media
  final Map datas = <String, GenRes>{};

  @override
  void onInit() {
    super.onInit();
    var args = Get.arguments;
    role = args['role'];
    type = args['type'];

    List<String> list;
    switch (type) {
      case CreateType.photo:
        list = role.genPhotoTags ?? [];
        generatTime = 10;
        generatText = LocaleKeys.ai_generating_image.tr;
        break;
      case CreateType.video:
        list = role.genVideoTags ?? [];
        generatTime = 15;
        generatText = LocaleKeys.ai_generating_video.tr;
        break;
    }
    effectList.assignAll(list);
  }

  void create() async {
    if (!UserHelper().isVip.value) {
      final from = type == CreateType.photo ? VipSource.creimg : VipSource.crevideo;
      IRouter.pushVip(from);
      return;
    }

    if (selectedEffect.isEmpty) {
      SmartDialog.showToast(
        type == CreateType.photo ? LocaleKeys.select_image_effect.tr : LocaleKeys.select_video_effect.tr,
      );
      return;
    }

    final data = datas[selectedEffect];
    if (data != null) {
      data0 = data;
      showResult();
      return;
    }

    final event = type == CreateType.photo ? 'c_createimg_con' : 'c_createvideo_con';
    logEvent(event);

    // 检查钻石
    final from = type == CreateType.photo ? ConsumeSource.creaimg : ConsumeSource.creavideo;
    if (!UserHelper().isBalanceEnough(from)) {
      IRouter.pushGem(from);
      return;
    }

    showLoading();
    try {
      final id = role.id;
      if (id == null) {
        return;
      }
      final data = await IP.genResult(id: id, mediaType: type.name, tag: selectedEffect.value);
      if (data == null) {
        SmartDialog.showToast(LocaleKeys.generate_error.tr);
        return;
      }
      data0 = data;
      datas[selectedEffect.value] = data;

      showResult();

      UserHelper().consume(from);
    } catch (e) {
      SmartDialog.showToast(LocaleKeys.generate_error.tr);
    }
  }

  void showResult() {
    if (isLoading.value) {
      return;
    }
    final mediaType = data0?.mediaType;
    if (mediaType == 'PHOTO') {
      showImage();
    } else if (mediaType == 'VIDEO') {
      showVideo();
    } else {
      SmartDialog.showToast(LocaleKeys.generate_error.tr);
    }
  }

  void showImage() {
    var url = data0?.uri;
    if (url == null) {
      return;
    }
    IRouter.pushImagePreview(url);
  }

  void showVideo() {
    var url = data0?.uri;
    if (url == null) {
      return;
    }
    IRouter.pushVideoPreview(url);
  }

  void showLoading() {
    isLoading.value = true;
  }

  void hiddenLoading() {
    isLoading.value = false;
  }
}
