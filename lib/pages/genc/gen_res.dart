import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/choose_btn.dart';
import '../../common/role_ana_row.dart';
import '../../common/s_button.dart';
import '../../common/s_image.dart';
import '../../common/s_progess.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/i_theme.dart';
import 'gen_res_c.dart';

class GenRes extends StatelessWidget {
  GenRes({super.key});

  final ctr = Get.put(GenResC());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: const Color(0xff222222),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Image.asset(
            Assets.images.newUi.navClose.path,
            width: 24,
          ),
        ),
        title: <PERSON><PERSON><PERSON><PERSON><PERSON>(role: ctr.role),
      ),
      body: Safe<PERSON>rea(
        bottom: false,
        child: Stack(
          children: [
            Positioned.fill(child: Assets.images.newUi.creatBg.image()),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 300,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      Color(0xff140D28),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 16,
              right: 16,
              bottom: 34,
              child: Column(
                children: [
                  Expanded(
                    child: SImage(
                      url: ctr.role.avatar,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: IColor.brand1,
                        width: 3,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  SButton(
                    title: LocaleKeys.btn_continue.tr,
                    type: SButtonType.primary,
                    onTap: () {
                      ctr.create();
                    },
                  )
                ],
              ),
            ),
            Positioned(
              left: 32,
              right: 32,
              bottom: 117,
              child: Container(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocaleKeys.select_effect.tr,
                      style: ITStyle.l3sem(IColor.n10),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      child: Obx(
                        () {
                          final list = ctr.effectList;
                          final selectedEffect = ctr.selectedEffect.value;

                          return GridView.builder(
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (_, index) {
                              final tag = list[index];
                              final isSelected = selectedEffect == tag;

                              return ChooseBtn(
                                title: tag,
                                isSelected: isSelected,
                                onTap: () {
                                  ctr.selectedEffect.value = tag;
                                  ctr.create();
                                },
                              );
                            },
                            itemCount: list.length,
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              childAspectRatio: 152.0 / 35.0,
                              mainAxisSpacing: 8,
                              crossAxisSpacing: 8,
                            ),
                          );
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
            Obx(() {
              var isLoading = ctr.isLoading.value;
              return isLoading
                  ? Positioned.fill(
                      child: Container(
                        color: Colors.black.withOpacity(0.5),
                        alignment: Alignment.center,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              ctr.generatText,
                              style: ITStyle.l4sem(IColor.n10),
                            ),
                            const SizedBox(height: 16),
                            SProgress(
                              duration: Duration(seconds: ctr.generatTime),
                              width: 250,
                              height: 4,
                              backgroundColor: const Color(0xFFF6F6FE),
                              progressColor: IColor.brand,
                              borderRadius: 4,
                              onProgress: (double value) {
                                if (value == 1.0) {
                                  ctr.hiddenLoading();
                                  ctr.showResult();
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    )
                  : Container();
            }),
          ],
        ),
      ),
    );
  }
}
