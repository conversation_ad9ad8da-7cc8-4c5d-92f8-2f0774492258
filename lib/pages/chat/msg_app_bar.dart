import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/i_dialog.dart';
import '../../common/s_image.dart';
import '../../gen/assets.gen.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import 'msg_ctr.dart';

class MsgAppBar extends StatelessWidget implements PreferredSizeWidget {
  // 通过 preferredSize 确保 AppBar 的高度符合标准
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  const MsgAppBar({
    super.key,
    required this.ctr,
  });

  final MsgCtr ctr;

  @override
  Widget build(BuildContext context) {
    var role = ctr.role;

    return AppBar(
      elevation: 0, // 去除阴影
      scrolledUnderElevation: 0,
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent, // 去除悬浮效果
      flexibleSpace: ClipRect(
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xff140D28),
                Colors.transparent,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      leading: IconButton(
        onPressed: () {
          Get.back();
        },
        icon: Assets.images.newUi.navBack.image(width: 24, color: Colors.white),
      ),
      titleSpacing: 0,
      title: Row(
        children: [
          _levelBtn(),
          const Spacer(),
          Container(
            height: 48,
            width: 168,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              splashColor: Colors.transparent, // 取消水波纹效果
              highlightColor: Colors.transparent, // 取消高亮效果
              onTap: () {
                IRouter.pushProfile(role);
              },
              child: Row(
                children: [
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          role.name ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: ITStyle.l4sem(Colors.white),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          role.aboutMe ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: ITStyle.l5reg(Colors.white),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: SImage(
                      url: role.avatar,
                      width: 40,
                      height: 40,
                      shape: BoxShape.rectangle,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
    );
  }

  Widget _levelBtn() {
    return Obx(() {
      final data = ctr.chatLevel.value;

      var level = data?.level ?? 1;

      final map = ctr.chatLevelConfigs.firstWhereOrNull((element) => element['level'] == level);
      var levelStr = map?['icon'] as String?;

      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          IDialog.showChatLevel();
        },
        child: Container(
          padding: const EdgeInsets.all(3.5),
          decoration: const BoxDecoration(
            gradient: LinearGradient(colors: IColor.brandjb),
            shape: BoxShape.circle,
          ),
          child: Text(
            levelStr ?? '👋',
            style: const TextStyle(fontSize: 20),
          ),
        ),
      );
    });
  }

  // Widget _buildLevel() {
  //   return Obx(() {
  //     final data = ctr.chatLevel.value;

  //     var level = data?.level ?? 1;

  //     final map = ctr.chatLevelConfigs.firstWhereOrNull((element) => element['level'] == level);
  //     var levelStr = map?['icon'] as String?;

  //     var progress = (data?.progress ?? 0) / 100.0;
  //     var rewards = '+${data?.rewards ?? 0}';

  //     // var value = formatNumber(data?.progress);
  //     // var total = data?.upgradeRequirements?.toInt() ?? 0;
  //     // var proText = '$value/$total';

  //     return Row(
  //       mainAxisAlignment: MainAxisAlignment.end,
  //       crossAxisAlignment: CrossAxisAlignment.center,
  //       children: [
  //         GestureDetector(
  //           behavior: HitTestBehavior.opaque,
  //           onTap: () {
  //             AppDialog.showChatLevel();
  //           },
  //           child: Container(
  //             padding: const EdgeInsets.all(2.5),
  //             decoration: const BoxDecoration(color: AppColor.brand, shape: BoxShape.circle),
  //             child: Center(
  //               child: Text(
  //                 levelStr ?? '👋',
  //                 style: const TextStyle(fontSize: 17),
  //               ),
  //             ),
  //           ),
  //         ),
  //         const SizedBox(width: 8),
  //         Text(
  //           'Lv $level',
  //           style: AppTestStyle.l4sem(AppColor.n10),
  //         ),
  //         const SizedBox(width: 8),
  //         SLevelProgress(progress: progress),
  //         const SizedBox(width: 8),
  //         Assets.images.imgGems.image(width: 12),
  //         Padding(
  //           padding: const EdgeInsets.only(bottom: 1),
  //           child: GradientText(
  //             data: rewards,
  //             gradient: const LinearGradient(
  //               colors: AppColor.brandjb,
  //               begin: Alignment.topCenter,
  //               end: Alignment.bottomCenter,
  //             ),
  //             style: AppTestStyle.l6sem(null),
  //           ),
  //         ),
  //         const SizedBox(width: 16),
  //       ],
  //     );
  //   });
  // }
}
