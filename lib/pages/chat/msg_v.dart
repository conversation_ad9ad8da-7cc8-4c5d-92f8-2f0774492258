import 'dart:io';

import 'package:bushy/common/d_background.dart';
import 'package:bushy/common/s_button.dart';
import 'package:bushy/common/s_image.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:bushy/pages/chat/call_bar.dart';
import 'package:bushy/pages/chat/float_items.dart';
import 'package:bushy/pages/chat/input_bar.dart';
import 'package:bushy/pages/chat/level_bar.dart';
import 'package:bushy/pages/chat/msg_app_bar.dart';
import 'package:bushy/pages/chat/msg_ctr.dart';
import 'package:bushy/pages/chat/photo_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../data/chater.dart';
import 'msg_tableview.dart';

class MsgV extends StatefulWidget {
  const MsgV({super.key});

  @override
  State<MsgV> createState() => _MsgVState();
}

class _MsgVState extends State<MsgV> {
  final ctr = Get.put(MsgCtr());

  final callHeight = 56.0;

  double floatButtonsY = 171.0;

  @override
  void initState() {
    super.initState();

    if (!ICache().isBusy || ctr.role.images == null || ctr.role.images!.isEmpty) {
      floatButtonsY = callHeight;
    }
  }

  void _getListViewTopY(double height) {
    floatButtonsY = height + callHeight;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final role = ctr.role;

    return Stack(
      children: [
        Positioned.fill(
          child: ICache().cbi.isNotEmpty ? Image.file(File(ICache().cbi), fit: BoxFit.cover) : SImage(url: role.avatar),
        ),
        _buildContent(role),
        Positioned.fill(
          child: Obx(() {
            final vip = UserHelper().isVip.value;
            if (role.vip == true && !vip) {
              return const RoleHotLock();
            }
            return const SizedBox();
          }),
        ),
      ],
    );
  }

  Scaffold _buildContent(Chater role) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      appBar: MsgAppBar(ctr: ctr),
      body: Stack(
        children: [
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Color(0xff140D28),
                  ],
                  stops: [0.2, 1.0],
                ),
              ),
            ),
          ),
          Column(
            children: [
              SafeArea(
                bottom: false,
                child: CallBar(
                  role: role,
                  sessionId: ctr.sessionId ?? 0,
                  height: callHeight,
                ),
              ),
              PhotoBar(
                expandChaged: (double height) {
                  _getListViewTopY(height);
                },
              ),
              const LevelBar(),
              const Expanded(child: MsgTableView()),
              const InputBar(),
            ],
          ),
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300), // 动画持续时间
            curve: Curves.easeInOut, // 动画曲线
            top: floatButtonsY,
            child: SafeArea(
              child: FloatItems(
                role: role,
                sessionId: ctr.sessionId ?? 0,
              ),
            ),
          )
        ],
      ),
    );
  }
}

class RoleHotLock extends StatelessWidget {
  const RoleHotLock({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Container(
            color: Colors.transparent,
            width: double.infinity,
            child: Stack(
              children: [
                InkWell(
                  onTap: () => Get.back(),
                  child: Container(
                    margin: const EdgeInsets.only(top: kToolbarHeight),
                    width: 60,
                    height: 60,
                  ),
                ),
              ],
            ),
          ),
        ),
        SheetBackground(
          onClose: () => Get.back(),
          child: Column(
            children: [
              Text(
                LocaleKeys.unlock_hot_roles.tr,
                textAlign: TextAlign.center,
                style: ITStyle.l3sem(Colors.black),
              ),
              const SizedBox(height: 10),
              Text(
                LocaleKeys.premium_unlock_message.tr,
                textAlign: TextAlign.center,
                style: ITStyle.l4reg(Colors.black),
              ),
              const SizedBox(height: 32),
              SButton(
                title: LocaleKeys.unlock_now.tr,
                onTap: () {
                  IRouter.pushVip(VipSource.viprole);
                },
                type: SButtonType.primary,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
