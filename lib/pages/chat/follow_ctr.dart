import 'package:bushy/common/i_empty.dart';
import 'package:bushy/data/chater.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/service/i_p.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FollowCtr extends GetxController {
  var list = <Chater>[].obs;

  int page = 1;
  int size = 100;
  var type = Rx<EmptyType?>(EmptyType.empty);
  bool isNoMoreData = false;

  final EasyRefreshController refCtr = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  @override
  void onInit() {
    super.onInit();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      onRefresh();
    });
  }

  @override
  void dispose() {
    refCtr.dispose();
    super.dispose();
  }

  Future<void> onRefresh() async {
    page = 1;
    await fetchData();
    refCtr.finishRefresh();
    refCtr.resetFooter();
  }

  Future<void> onLoad() async {
    page++;
    await fetchData();
    refCtr.finishLoad(isNoMoreData ? IndicatorResult.noMore : IndicatorResult.none);
  }

  Future<void> fetchData() async {
    try {
      final res = await IP.collectList(page, size);
      isNoMoreData = (res?.records?.length ?? 0) < size;
      if (page == 1) {
        list.clear();
      }
      list.addAll(res?.records ?? []);

      type.value = list.isEmpty ? EmptyType.chat : null;
    } catch (e) {
      type.value = list.isEmpty ? EmptyType.chat : null;
      if (page > 1) page--;
    }
  }

  Future<void> onItemTap(Chater role) async {
    IRouter.pushChat(role.id);
  }
}
