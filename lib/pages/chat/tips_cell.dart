import 'package:bushy/common/s_blur_background.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TipsCell extends StatelessWidget {
  const TipsCell({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SBlurBackground(
        blur: 25,
        backgroundColor: IColor.black5,
        radius: 4,
        padding: const EdgeInsets.all(8),
        child: Text(
          LocaleKeys.replies_generated_by_ai_and_fictional.tr,
          style: ITStyle.l6reg(IColor.n10),
        ),
      ),
    );
  }
}
