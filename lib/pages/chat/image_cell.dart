import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/s_image.dart';
import '../../data/msg.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/event_util.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';
import '../../other/user_helper.dart';
import 'text_cell.dart';

class ImageCell extends StatelessWidget {
  const ImageCell({
    super.key,
    required this.msg,
  });

  final Msg msg;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.centerStart,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextCell(msg: msg),
          const SizedBox(height: 8),
          _buildImageWidget(context),
        ],
      ),
    );
  }

  Widget _buildImageWidget(BuildContext context) {
    var imageUrl = msg.imgUrl ?? '';
    var isLockImage = msg.mediaLock == LockLevel.private.value;
    var imageWidth = 200.0;
    var imageHeight = 240.0;

    var imageWidget = ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: SImage(
        url: imageUrl,
        width: imageWidth,
        height: imageHeight,
        borderRadius: BorderRadius.circular(16),
      ),
    );

    var isHide = !UserHelper().isVip.value && isLockImage;

    return isHide
        ? GestureDetector(
            onTap: _onTapUnlock,
            child: Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
              ),
              clipBehavior: Clip.antiAlias,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  imageWidget,
                  ClipRect(
                    child: BackdropFilter(
                      blendMode: BlendMode.srcIn,
                      filter: ImageFilter.blur(sigmaX: 65, sigmaY: 65),
                      child: Container(
                        alignment: Alignment.center,
                        height: double.infinity,
                        width: double.infinity,
                        decoration: BoxDecoration(color: Colors.black.withOpacity(0.6)),
                      ),
                    ),
                  ),
                  _buildContentButton(),
                ],
              ),
            ),
          )
        : GestureDetector(
            onTap: () {
              IRouter.pushImagePreview(imageUrl);
            },
            child: imageWidget,
          );
  }

  Column _buildContentButton() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Assets.images.newUi.lock.image(width: 32),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: const BorderRadius.all(Radius.circular(30)),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: const LinearGradient(
                        colors: IColor.brandjb,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: const Text(
                      'NSFW',
                      style: TextStyle(color: Colors.black, fontSize: 8),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    LocaleKeys.hot_photo.tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _onTapUnlock() async {
    logEvent('c_news_lockpic');
    final isVip = UserHelper().isVip.value;
    if (!isVip) {
      IRouter.pushVip(VipSource.lockpic);
    }
  }
}
