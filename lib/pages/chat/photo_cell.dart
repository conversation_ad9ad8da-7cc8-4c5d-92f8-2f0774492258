import 'dart:ui';

import 'package:bushy/common/s_image.dart';
import 'package:bushy/data/chater.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

class PhotoCell extends StatelessWidget {
  const PhotoCell({
    super.key,
    required this.image,
    this.imageHeight,
    required this.onTapPic,
    required this.onTap,
    required this.unlocked,
  });

  final RoleImage image;
  final bool unlocked;
  final double? imageHeight;
  final void Function() onTapPic;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: imageHeight,
      width: imageHeight,
      child: Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: onTapPic,
              child: SImage(
                url: image.imageUrl,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          if (!unlocked)
            Positioned.fill(
              child: GestureDetector(
                onTap: onTap,
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.all(Radius.circular(4)),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Assets.images.newUi.gold.image(width: 16, height: 16),
                              const SizedBox(width: 4),
                              Text(
                                '${image.gemTally ?? 0}',
                                style: ITStyle.l5sem(Colors.white),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
        ],
      ),
    );
  }
}
