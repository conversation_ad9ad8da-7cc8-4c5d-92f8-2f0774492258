import 'package:bushy/common/s_image.dart';
import 'package:bushy/data/msg.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/pages/chat/text_cell.dart';
import 'package:flutter/material.dart';

class ClotheCell extends StatelessWidget {
  const ClotheCell({super.key, required this.msg});
  final Msg msg;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.centerStart,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextCell(msg: msg),
          const SizedBox(height: 8),
          _buildImageWidget(context),
        ],
      ),
    );
  }

  Widget _buildImageWidget(BuildContext context) {
    var imageUrl = msg.giftImg ?? '';
    var imageWidth = 200.0;
    var imageHeight = 240.0;

    var imageWidget = ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: SImage(
        url: imageUrl,
        width: imageWidth,
        height: imageHeight,
        borderRadius: BorderRadius.circular(16),
      ),
    );

    return GestureDetector(
      onTap: () {
        IRouter.pushImagePreview(imageUrl);
      },
      child: imageWidget,
    );
  }
}
