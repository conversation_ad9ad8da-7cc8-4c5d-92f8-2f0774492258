import 'package:bushy/other/i_enum.dart';
import 'package:bushy/pages/home/<USER>';
import 'package:flutter/material.dart';

import '../../data/chater.dart';

class CallBar extends StatelessWidget {
  const CallBar({super.key, required this.role, required this.sessionId, required this.height});

  final Chater role;
  final int sessionId;
  final double height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: const Row(
        children: [
          // InkWell(
          //   splashColor: Colors.transparent, // 取消水波纹效果
          //   highlightColor: Colors.transparent, // 取消高亮效果
          //   onTap: () {
          //     logEvent('c_call');
          //     if (!UserTool().isVip.value) {
          //       RouterTool.pushVip(VipFrom.call);
          //       return;
          //     }

          //     if (!UserTool().isBalanceEnough(ConsumeFrom.call)) {
          //       RouterTool.pushGem(ConsumeFrom.call);
          //       return;
          //     }
          //     if (sessionId <= 0) {
          //       SmartDialog.showToast(LocaleKeys.some_error_try_again.tr);
          //       log.e('sessionId <= 0');
          //       return;
          //     }

          //     RouterTool.pushPhone(sessionId: sessionId, role: role, showVideo: false);
          //   },
          //   child: Container(
          //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          //     child: Image.asset(Assets.images.icMsgPhone.path, width: 24, height: 24),
          //   ),
          // ),
          Spacer(),
          CoinBtn(from: ConsumeSource.chat),
          SizedBox(width: 16),
        ],
      ),
    );
  }
}
