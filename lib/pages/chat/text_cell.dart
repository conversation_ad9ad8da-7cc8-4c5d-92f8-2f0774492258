import 'package:bushy/common/s_blur_background.dart';
import 'package:bushy/common/s_image.dart';
import 'package:bushy/data/msg.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_fun.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:bushy/pages/chat/lock_cell.dart';
import 'package:bushy/pages/chat/msg_ctr.dart';
import 'package:bushy/pages/chat/rich_text_cell.dart';
import 'package:bushy/pages/chat/send_cell.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// enum MsgPlayState {
//   none,
//   playing,
//   downloading,
// }

class TextCell extends StatefulWidget {
  const TextCell({super.key, required this.msg});

  final Msg msg;

  @override
  State<TextCell> createState() => _TextCellState();
}

class _TextCellState extends State<TextCell> {
  MsgCtr ctr = Get.find<MsgCtr>();

  final hasVoice = false;
  // late String timer = '1s';
  // final MsgPlayState _playState = MsgPlayState.none;

  // final bool _autoPlay = false;

  @override
  void initState() {
    super.initState();

    // hasVoice = widget.msg.voiceUrl != null && widget.msg.voiceDur != null;

    // _autoPlay =
    //     ctr.isNewChat && widget.msg.source == MsgSource.welcome && CacheTool().isb && _playState == MsgPlayState.none;

    // if (_autoPlay) {
    //   _startAudioPlay();
    // }
  }

  @override
  void dispose() {
    super.dispose();
    // AudioPlayTool().stopAll();
  }

  // void _startAudioPlay() async {
  //   logEvent('c_news_voice');
  //   if (!UserTool().isVip.value &&
  //       widget.msg.source != MsgSource.welcome &&
  //       !UserTool().isBalanceEnough(ConsumeFrom.audio)) {
  //     RouterTool.pushGem(ConsumeFrom.audio);
  //     return;
  //   }

  //   if (_playState == MsgPlayState.downloading) {
  //     return;
  //   }

  //   if (_playState == MsgPlayState.playing) {
  //     _stopAudioPlay();
  //     _stopPlayAni();
  //     return;
  //   }

  //   var url = widget.msg.voiceUrl;
  //   var duration = widget.msg.voiceDur ?? 0;

  //   setState(() {
  //     _playState = MsgPlayState.downloading;
  //   });

  //   if (url != null) {
  //     final filePath = await DownloadTool.download(url);
  //     if (filePath == null || filePath.isEmpty) {
  //       setState(() {
  //         _playState = MsgPlayState.none;
  //       });
  //       return;
  //     }

  //     if (!_autoPlay && !UserTool().isVip.value) {
  //       UserTool().consume(ConsumeFrom.audio);
  //     }

  //     if (mounted) {
  //       _playAudio(filePath, duration);
  //     }
  //   } else {
  //     setState(() {
  //       _playState = MsgPlayState.none;
  //     });
  //     SmartDialog.showToast('this audio url is not available');
  //   }
  // }

  // void _playAudio(String path, int duration) async {
  //   if (_playState == MsgPlayState.playing) {
  //     return;
  //   }
  //   final reslut = await AudioPlayTool().play(
  //     widget.msg.id.toString(),
  //     DeviceFileSource(path),
  //     stopAction: _stopPlayAni,
  //     position: Duration.zero,
  //   );

  //   if (reslut) {
  //     _playState = MsgPlayState.playing;
  //   } else {
  //     _playState = MsgPlayState.none;
  //   }
  //   if (mounted) {
  //     setState(() {});
  //   }
  // }

  // void _stopAudioPlay() {
  //   AudioPlayTool().stop(widget.msg.id.toString());
  // }

  // void _stopPlayAni() {
  //   if (mounted) {
  //     setState(() {
  //       _playState = MsgPlayState.none;
  //     });
  //     if (_autoPlay) {
  //       ctr.isNewChat = false;
  //     }
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    var msg = widget.msg;
    final sendText = msg.question;
    final receivText = msg.answer;

    final noSend = msg.source == MsgSource.gift || msg.source == MsgSource.clothe;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (sendText != null && (msg.onAnswer == false) && !noSend)
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SendCell(msg: msg),
              const SizedBox(height: 16),
            ],
          ),
        if (receivText != null)
          Align(
            alignment: AlignmentDirectional.centerStart,
            child: Obx(() {
              final isVip = UserHelper().isVip.value;
              final lock = widget.msg.textLock == LockLevel.private.value;
              if (!isVip && lock) {
                return const LockCell();
              }
              return Stack(
                children: [
                  _buildText(context),
                  // if (hasVoice)
                  //   Positioned(
                  //     top: 0,
                  //     left: 0,
                  //     child: _buildPlayButton(),
                  //   ),
                ],
              );
            }),
          ),
      ],
    );
  }

  Widget _buildText(BuildContext context) {
    // hasVoice = widget.msg.voiceUrl != null && widget.msg.voiceDur != null;
    // timer = formatAudioTime(widget.msg.voiceDur ?? 0);

    final msg = widget.msg;
    final content = msg.answer ?? '';
    final translateAnswer = msg.translateAnswer ?? content;

    var showTransBtn = false;
    var showUndo = false;
    var showTrans = true;

    // var showTransBtn = true;
    // var showUndo = true;
    // var showTrans = false;

    // // 获取用户自动翻译设置
    // final autoTranslate = UserHelper().user?.autoTranslate ?? false;

    // if (autoTranslate) {
    //   showTransBtn = false;
    //   showUndo = false;
    //   showTrans = translateAnswer.isNotEmpty;
    // } else {
    //   showTrans = msg.showTranslate ?? translateAnswer.isNotEmpty;
    //   showUndo = showTrans;
    //   // 如果是英文设备语言，隐藏翻译按钮
    //   showTransBtn = (Get.deviceLocale?.languageCode != 'en') && !showUndo;
    // }

    return Container(
      padding: hasVoice ? const EdgeInsets.only(top: 15) : EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          IntrinsicWidth(
            child: SBlurBackground(
              blur: 46,
              padding: hasVoice
                  ? const EdgeInsetsDirectional.only(top: 18, start: 12, end: 12, bottom: 12)
                  : const EdgeInsets.all(12),
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.8,
              ),
              backgroundColor: Colors.white.withOpacity(0.2),
              borderRadius: const BorderRadiusDirectional.only(
                topEnd: Radius.circular(16),
                bottomStart: Radius.circular(16),
                bottomEnd: Radius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _buildMsgContent(
                    translateAnswer: translateAnswer,
                    content: content,
                    msg: msg,
                    showTrans: showTrans,
                  ),
                  _buildBottomRow(showUndo),
                ],
              ),
            ),
          ),
          if (!msg.typewriterAnimated) _buildActionButtons(showTransBtn, showUndo),
        ],
      ),
    );
  }

  Row _buildBottomRow(bool showUndo) {
    final msg = widget.msg;

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (showUndo) _buildUndoButton(),
        const SizedBox(width: 8),
        if (msg.source == MsgSource.gift) _buildGiftImage(),
      ],
    );
  }

  RichTextCell _buildMsgContent({
    required String translateAnswer,
    required String content,
    required Msg msg,
    required bool showTrans,
  }) {
    return RichTextCell(
      text: showTrans ? translateAnswer : content,
      isSend: false,
      isTypingAnimation: msg.typewriterAnimated,
      onAnimationComplete: () {
        // 打字动画完成后的回调
        if (msg.typewriterAnimated) {
          setState(() {
            msg.typewriterAnimated = false;
          });
        }
      },
    );
  }

  Widget _buildActionButtons(bool showTransBtn, bool showTranslate) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!ICache().isBusy)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              child: _buildReportButton(),
            ),
          // if (showTransBtn)
          //   Padding(
          //     padding: const EdgeInsets.symmetric(horizontal: 2),
          //     child: _buildTransButton(showTranslate),
          //   ),
        ],
      ),
    );
  }

  InkWell _buildUndoButton() {
    return InkWell(
      onTap: () {
        widget.msg.showTranslate = false;
        setState(() {});
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        margin: const EdgeInsets.only(top: 2),
        decoration: BoxDecoration(
          color: IColor.brand,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          LocaleKeys.undo.tr,
          style: ITStyle.l6sem(Colors.white),
        ),
      ),
    );
  }

  InkWell _buildReportButton() {
    return InkWell(
      onTap: () => report(),
      child: SBlurBackground(
        blur: 46,
        backgroundColor: IColor.black5,
        padding: const EdgeInsets.all(5),
        borderRadius: BorderRadius.circular(4),
        child: Center(
          child: Text(LocaleKeys.report.tr, style: ITStyle.l6sem(IColor.n10)),
        ),
      ),
    );
  }

  // InkWell _buildTransButton(bool showTranslate) {
  //   return InkWell(
  //     onTap: () => ctr.translateMsg(widget.msg),
  //     child: SBlurBackground(
  //       blur: 46,
  //       backgroundColor: IColor.black5,
  //       padding: const EdgeInsets.all(4),
  //       borderRadius: BorderRadius.circular(4),
  //       child: Center(
  //         child: SvgIcon(
  //           assetName: Assets.images.newUi.trans,
  //           width: 16,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildPlayButton() {
  //   return InkWell(
  //     onTap: _startAudioPlay,
  //     child: Container(
  //       height: 30,
  //       padding: const EdgeInsets.symmetric(horizontal: 12),
  //       decoration: const BoxDecoration(
  //         color: AppColor.brand,
  //         borderRadius: BorderRadiusDirectional.only(
  //           topStart: Radius.circular(8),
  //           topEnd: Radius.circular(8),
  //           bottomEnd: Radius.circular(8),
  //         ),
  //       ),
  //       child: Center(
  //         child: Row(
  //           children: [
  //             Center(
  //               child: Row(
  //                 mainAxisSize: MainAxisSize.min,
  //                 children: [
  //                   _playIcon,
  //                   const SizedBox(width: 4),
  //                   Text(timer, style: AppTestStyle.l5reg(Colors.white)),
  //                 ],
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // Widget get _playIcon {
  //   Widget icon;
  //   switch (_playState) {
  //     case MsgPlayState.none:
  //       icon = Assets.images.icPlay.image(width: 10, color: Colors.white);
  //       break;
  //     case MsgPlayState.downloading:
  //       icon = LoadingAnimationWidget.inkDrop(
  //         color: Colors.black,
  //         size: 10,
  //       );
  //       break;
  //     case MsgPlayState.playing:
  //       icon = LoadingAnimationWidget.staggeredDotsWave(
  //         color: Colors.black,
  //         size: 20,
  //       );
  //       break;
  //   }
  //   return icon;
  // }

  Widget _buildGiftImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4),
      child: InkWell(
        onTap: () {
          IRouter.pushImagePreview(widget.msg.giftImg ?? '');
        },
        child: Container(
          width: 30,
          height: 40,
          color: Colors.white,
          child: SImage(url: widget.msg.giftImg),
        ),
      ),
    );
  }
}
