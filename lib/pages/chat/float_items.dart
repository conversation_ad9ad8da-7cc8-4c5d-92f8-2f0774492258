import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../common/s_image.dart';
import '../../data/chater.dart';
import '../../generated/locales.g.dart';
import '../../other/event_util.dart';
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/i_theme.dart';

class FloatItems extends StatelessWidget {
  const FloatItems({super.key, required this.role, required this.sessionId});

  final Chater role;
  final int sessionId;

  @override
  Widget build(BuildContext context) {
    if (ICache().isBusy) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (role.videoChat == true) _buildVideoItem(),
                if (role.genVideo == true) _buildItem('🔥', LocaleKeys.gen_video.tr, _onTapCreateVideo),
                if (role.genPhoto == true) _buildItem('🌶', LocaleKeys.gen_picture.tr, _onTapCreateImage),
              ],
            ),
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  void _onTapPhoneVideo() {
    logEvent('c_videocall');
    IRouter.pushPhoneGuide(role: role);
  }

  void _onTapCreateImage() {
    logEvent('c_createimg');
    IRouter.pushCreate(role: role, type: CreateType.photo);
  }

  void _onTapCreateVideo() {
    logEvent('c_createvideo');
    IRouter.pushCreate(role: role, type: CreateType.video);
  }

  Widget _buildVideoItem() {
    final guide = role.characterVideoChat?.firstWhereOrNull((e) => e.tag == 'guide');
    final url = guide?.gifUrl ?? role.avatar;

    return GestureDetector(
      onTap: _onTapPhoneVideo,
      child: Stack(
        alignment: AlignmentDirectional.bottomEnd,
        children: [
          Container(
            width: 72,
            height: 72,
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: IColor.brandjb,
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(36),
            ),
            child: SImage(
              url: url,
              width: 72,
              height: 72,
              borderRadius: BorderRadius.circular(36),
            ),
          ),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: IColor.brand,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Icon(Icons.videocam_sharp, size: 12, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItem(String icon, String name, void Function() onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 28,
        margin: const EdgeInsets.only(top: 12),
        decoration: BoxDecoration(
          color: IColor.brand,
          borderRadius: BorderRadius.circular(14),
        ),
        child: LayoutBuilder(
          builder: (BuildContext _, BoxConstraints bc) {
            // 创建一个 TextPainter 来计算文本的宽度
            TextStyle textStyle = GoogleFonts.montserrat(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            );

            TextPainter textPainter = TextPainter(
              text: TextSpan(text: '🔥  $name', style: textStyle),
              textDirection: TextDirection.ltr,
            );
            textPainter.layout();
            // 获取文本宽度
            double textWidth = textPainter.width;
            final width = textWidth + 20;

            return Container(
              width: width,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Center(
                child: Row(
                  children: [
                    const SizedBox(width: 4),
                    Text(icon, style: textStyle),
                    const SizedBox(width: 2),
                    Text(name, style: textStyle),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
