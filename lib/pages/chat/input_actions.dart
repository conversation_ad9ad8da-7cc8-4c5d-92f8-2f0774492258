import 'dart:ui';

import 'package:bushy/data/input_btn_model.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/svg_icon.dart';
import 'package:bushy/pages/chat/msg_ctr.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

class InputActions extends StatefulWidget {
  const InputActions({super.key, required this.onTap, required this.tags});

  final Function(int) onTap;

  final List<InputBtnModel> tags;

  @override
  State<InputActions> createState() => _InputActionsState();
}

class _InputActionsState extends State<InputActions> {
  bool isBlock = false;
  final ctr = Get.find<MsgCtr>();

  @override
  Widget build(BuildContext context) {
    if (!ICache().isBusy) {
      return _buildBlock();
    }

    return Container(
      alignment: Alignment.bottomCenter,
      margin: const EdgeInsets.only(top: 8),
      child: SizedBox(
        height: 20,
        child: ListView.separated(
          physics: const BouncingScrollPhysics(),
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemBuilder: (context, index) {
            final item = widget.tags[index];

            var bgColor = Colors.white.withOpacity(0.2);
            var textColor = const Color(0xff906BF7);
            var icon = item.icon;
            var title = item.name;

            return _buildItem(index, bgColor, icon, title, textColor);
          },
          separatorBuilder: (context, index) => const SizedBox(width: 8),
          itemCount: widget.tags.length,
        ),
      ),
    );
  }

  Widget _buildBlock() {
    return Row(
      children: [
        const SizedBox(width: 16),
        InkWell(
          onTap: () async {
            final blockId = ctr.role.id;
            if (isBlock) {
              ICache().removeBlockId(blockId);
              await SmartDialog.showToast('The character was unblocked successfully');
              setState(() {
                isBlock = false;
              });
            } else {
              ICache().addBlockId(blockId);
              await SmartDialog.showToast('The character was blocked successfully');
              setState(() {
                isBlock = true;
              });
            }
          },
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                height: 20,
                color: Colors.white.withOpacity(0.2),
                constraints: const BoxConstraints(minWidth: 54),
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  isBlock ? 'Unblock' : 'Block',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: ITStyle.l5reg(const Color(0xff906BF7)),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  InkWell _buildItem(int index, Color bgColor, icon, title, Color textColor) {
    return InkWell(
      onTap: () => widget.onTap(index),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            height: 20,
            color: bgColor,
            constraints: const BoxConstraints(minWidth: 54),
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgIcon(assetName: icon, width: 12),
                const SizedBox(width: 1.5),
                Text(
                  title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: ITStyle.l5reg(textColor),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
