import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:lottie/lottie.dart';

import '../../common/s_blur_background.dart';
import '../../data/msg.dart';
import '../../gen/assets.gen.dart';
import '../../generated/locales.g.dart';
import '../../other/a_play_util.dart';
import '../../other/event_util.dart';
import '../../other/i_download.dart';
import '../../other/i_enum.dart';
import '../../other/i_router.dart';
import '../../other/user_helper.dart';
import 'text_cell.dart';

enum PlayState {
  downloading,
  playing,
  paused,
  stopped,
}

class AudioCell extends StatefulWidget {
  const AudioCell({super.key, required this.msg});

  final Msg msg;

  @override
  State<AudioCell> createState() => _ChatMsgVoiceWidgetState();
}

class _ChatMsgVoiceWidgetState extends State<AudioCell>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 返回 true 以保持状态
  @override
  bool get wantKeepAlive => true;

  AnimationController? _controller;

  var playState = PlayState.stopped.obs;

  @override
  void initState() {
    super.initState();

    final msgId = widget.msg.id.toString();

    APlayUtil().getCurrentPosition(msgId).then((value) {
      //如果消息未播放完成则恢复动画
      if (value != null) {
        var duration = widget.msg.audioDuration ?? 0;
        var durLast = duration - value.inMilliseconds;
        playState.value = PlayState.playing;
        _startPlayAni(durLast);
      }
    });

    // 初始化 AnimationController
    _controller = AnimationController(vsync: this);
  }

  @override
  void dispose() {
    // _timer?.cancel();
    _stopAudioPlay();
    _controller?.dispose();
    super.dispose();
  }

  int _getAudioLen(Msg msg) {
    int len = msg.audioDuration ?? 0;
    return len.truncate();
  }

  Widget _getAudioUI(Msg msg) {
    return Row(
      children: [
        Obx(() {
          switch (playState.value) {
            case PlayState.downloading:
              return Center(
                child: LoadingAnimationWidget.discreteCircle(
                  color: Colors.white,
                  secondRingColor: Colors.white,
                  thirdRingColor: Colors.white,
                  size: 14,
                ),
              );
            case PlayState.playing:
              return Assets.images.newUi.audioPlaying.image(width: 20);
            default:
              return Assets.images.newUi.audioStop.image(width: 20);
          }
        }),
        const SizedBox(width: 8),
        Expanded(
          child: ColorFiltered(
            colorFilter: const ColorFilter.mode(
              Colors.white, // 你想要的颜色
              BlendMode.srcIn, // 或者尝试其他混合模式如 modulate, multiply
            ),
            child: Lottie.asset(
              'assets/lottie/audio.json',
              controller: _controller,
              fit: BoxFit.fill,
              onLoaded: (composition) {
                _controller?.duration = composition.duration;
              },
            ),
          ),
        ),
      ],
    );
  }

  void _startAudioPlay(Msg msg) async {
    playState.value = PlayState.downloading;

    var url = msg.audioUrl ?? '';
    var duration = msg.audioDuration ?? 0;

    final filePath = await IDownload.download(url);
    if (filePath == null || filePath.isEmpty) {
      playState.value = PlayState.stopped;
      return;
    }

    if (mounted) {
      _playAudio(filePath, duration);
    }
  }

  void _playAudio(String path, int duration) async {
    APlayUtil().play(widget.msg.id.toString(), DeviceFileSource(path), stopAction: _stopPlayAni).then(
      (value) {
        debugPrint('play audio: $value');
        if (value) {
          _startPlayAni(duration);
        } else {
          playState.value = PlayState.stopped;
        }
      },
    );
  }

  void _stopAudioPlay() {
    APlayUtil().stop(widget.msg.id.toString());
    _stopPlayAni();
  }

  void _startPlayAni(int duration) {
    if (playState.value == PlayState.playing) {
      _stopAudioPlay();
      return;
    }
    playState.value = PlayState.playing;

    if (mounted) {
      _controller?.forward(from: 0.0).then((_) {
        _controller?.repeat();
      });
    }
  }

  void _stopPlayAni() {
    if (mounted) {
      _controller?.stop();
    }

    playState.value = PlayState.stopped;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var isRead = widget.msg.isRead;
    var isShowTrial = !UserHelper().isVip.value;

    return Column(
      children: [
        TextCell(msg: widget.msg),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildAudio(isShowTrial, isRead),
          ],
        ),
      ],
    );
  }

  Widget _buildAudio(bool isShowTrial, bool isRead) {
    return Stack(
      alignment: Alignment.topLeft,
      children: [
        GestureDetector(
          onTap: () {
            if (!UserHelper().isVip.value) {
              logEvent('c_news_lockaudio');
              IRouter.pushVip(VipSource.lockaudio);
              return;
            }
            if (playState.value == PlayState.paused || playState.value == PlayState.stopped) {
              _startAudioPlay(widget.msg);
            } else {
              _stopAudioPlay();
            }
            if (!isRead) {
              // MsgService.to.markMessageAsRead(widget.msg.id);
            }
          },
          child: Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 130,
                  height: 62,
                  child: SBlurBackground(
                    blur: 0.1,
                    backgroundColor: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: _getAudioUI(widget.msg),
                  ),
                ),
                // if (!isRead)
                // Container(
                //   width: 8,
                //   height: 8,
                //   margin: const EdgeInsets.only(left: 8),
                //   decoration: BoxDecoration(
                //     borderRadius: BorderRadius.circular(4),
                //     color: AppColor.red,
                //   ),
                // )
              ],
            ),
          ),
        ),
        _buildTag()
      ],
    );
  }

  Container _buildTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      alignment: Alignment.centerLeft,
      decoration: const BoxDecoration(
        borderRadius: BorderRadiusDirectional.only(
          topStart: Radius.circular(4),
          topEnd: Radius.circular(16),
          bottomEnd: Radius.circular(16),
        ),
        color: Color(0xffFFA942),
      ),
      child: Row(
        children: [
          Text(
            LocaleKeys.moans_for_you.tr,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
