import 'package:bushy/common/i_empty.dart';
import 'package:bushy/common/s_image.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/pages/chat/session_ctr.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionView extends StatelessWidget {
  SessionView({super.key});

  final ctr = Get.put(SessionCtr());

  @override
  Widget build(BuildContext context) {
    return EasyRefresh.builder(
      controller: ctr.refCtr,
      onRefresh: ctr.onRefresh,
      onLoad: ctr.onLoad,
      childBuilder: (context, physics) {
        return Obx(() {
          if (ctr.type.value != null || ctr.list.isEmpty) {
            return IEmpty(type: ctr.type.value!, physics: physics);
          }
          return ListView.separated(
            physics: physics,
            padding: const EdgeInsets.symmetric(vertical: 16),
            itemBuilder: (_, index) {
              final item = ctr.list[index];

              return InkWell(
                splashColor: Colors.transparent, // 去除水波纹
                highlightColor: Colors.transparent, // 去除点击高亮
                onTap: () => ctr.onItemTap(item),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  height: 48,
                  child: Row(
                    children: [
                      SImage(
                        url: item.avatar,
                        width: 48,
                        height: 48,
                        shape: BoxShape.circle,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.title ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: ITStyle.l4sem(IColor.n02),
                            ),
                            Text(
                              item.lastMessage ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: ITStyle.l5sem(IColor.n05),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (_, idex) {
              return const SizedBox(height: 16);
            },
            itemCount: ctr.list.length,
          );
        });
      },
    );
  }
}
