import 'package:bushy/common/s_image.dart';
import 'package:bushy/data/chater.dart';
import 'package:bushy/data/clothe_model.dart';
import 'package:bushy/data/clothing_data.dart';
import 'package:bushy/data/gift.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/event_util.dart';
import 'package:bushy/other/i_config.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:bushy/pages/chat/msg_ctr.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GiftList extends StatefulWidget {
  const GiftList({super.key, required this.role, required this.ctr});

  final Chater role;
  final MsgCtr ctr;

  @override
  State<GiftList> createState() => _GiftListState();
}

class _GiftListState extends State<GiftList> {
  var index = 1;

  List<Gift>? gifts;
  Gift? chooseGift;

  ClothingData? chooseClothing;
  bool showClothing = false;
  List<ClothingData>? clothings;

  @override
  void initState() {
    super.initState();

    gifts = IConfig().gifts;
    chooseGift = gifts?.firstOrNull;

    List<ClothingData>? configClothings = IConfig().clothings;
    List<ClotheModel>? roleClothings = widget.role.changeClothes;

    if (configClothings == null || roleClothings == null) {
      return;
    }

    // Filter clothings by matching kitType
    clothings = [
      for (var e in configClothings)
        if (roleClothings.any((r) => e.kitType == r.clothingType)) e
    ];

    // Choose the first clothing if available
    chooseClothing = clothings?.isNotEmpty == true ? clothings?.first : null;

    // Check if clothing should be shown
    showClothing = widget.role.changeClothing == true && (clothings?.isNotEmpty == true);

    index = showClothing ? 0 : 1;

    setState(() {});
  }

  void onSend() {
    logEvent('c_gift');
    if (index == 0) {
      if (chooseClothing != null) {
        final prc = chooseClothing!.prc ?? 0;
        if (UserHelper().balance.value < prc) {
          IRouter.pushGem(ConsumeSource.gift_clo);
          return;
        }
        widget.ctr.sendClothing(chooseClothing!);
      }
    } else {
      if (chooseGift != null) {
        final prc = chooseGift!.prc ?? 0;
        if (UserHelper().balance.value < prc) {
          IRouter.pushGem(ConsumeSource.gift_toy);
          return;
        }
        widget.ctr.sendGift(chooseGift!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      top: true,
      child: Padding(
        padding: const EdgeInsets.only(top: kToolbarHeight + 56),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              children: [
                _buildType(),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: _buildSend(),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildItems(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildItems() {
    if (showClothing) {
      return index == 0 ? _buildClothingItems() : _buildToysItems();
    } else {
      return _buildToysItems();
    }
  }

  Widget _buildToysItems() {
    var list = gifts;

    if (list == null || list.isEmpty) {
      return const SizedBox();
    }
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 168.0 / 255.0,
      ),
      itemBuilder: (BuildContext context, int index) {
        Gift item = list[index];
        return _buildItem(
          isSelected: chooseGift?.id == item.id,
          imgUrl: item.img,
          name: item.perkName,
          price: item.prc,
          onTap: () {
            chooseGift = item;
            setState(() {});
          },
        );
      },
      itemCount: list.length,
    );
  }

  Widget _buildClothingItems() {
    var list = clothings;
    if (list == null || list.isEmpty) {
      return const SizedBox();
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 168.0 / 255.0,
      ),
      itemBuilder: (BuildContext context, int index) {
        ClothingData item = list[index];
        return _buildItem(
          isSelected: chooseClothing?.id == item.id,
          imgUrl: item.img,
          name: item.kitName,
          price: item.prc,
          onTap: () {
            chooseClothing = item;
            setState(() {});
          },
        );
      },
      itemCount: list.length,
    );
  }

  Widget _buildItem({
    required bool isSelected,
    required String? imgUrl,
    required String? name,
    required int? price,
    required void Function() onTap,
  }) {
    return Column(
      children: [
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              children: [
                Positioned.fill(
                  child: InkWell(
                    onTap: onTap,
                    child: SImage(
                      url: imgUrl,
                      width: double.infinity,
                      border: Border.all(
                        color: isSelected ? IColor.brand : const Color(0xffF2F2F2),
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                if (isSelected)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: IColor.brand2,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                if (isSelected) Center(child: Assets.images.newUi.check.image(width: 24)),
                PositionedDirectional(
                  top: 8,
                  end: 8,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4.5),
                        height: 24,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: IColor.black5,
                        ),
                        child: Row(
                          children: [
                            Assets.images.newUi.gold.image(width: 16, height: 16),
                            const SizedBox(width: 4),
                            Text(
                              '$price',
                              style: ITStyle.l5sem(Colors.white),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        Center(
          child: Text(
            name ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: ITStyle.l5sem(Colors.black),
          ),
        )
      ],
    );
  }

  Widget _buildSend() {
    return Row(
      children: [
        showClothing
            ? index == 0
                ? Text(
                    LocaleKeys.send_a_gift_and_get_a_picture.tr,
                    style: ITStyle.l6reg(const Color(0xffE96A68)),
                  )
                : const SizedBox.shrink()
            : Text(
                LocaleKeys.toys.tr,
                style: ITStyle.l21sem(IColor.n10),
              ),
        const Spacer(),
        InkWell(
          onTap: onSend,
          child: Container(
            height: 24,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(12)),
              color: IColor.brand,
            ),
            child: Center(
              child: Text(
                LocaleKeys.send.tr,
                style: ITStyle.l5sem(Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildType() {
    return Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: IColor.brandjb,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          height: 53,
          padding: const EdgeInsets.only(top: 8),
          child: Row(
            children: [
              if (showClothing)
                Expanded(
                  child: InkWell(
                    onTap: () {
                      index = 0;
                      setState(() {});
                    },
                    child: _buildTypeItem1(0, LocaleKeys.clothing.tr),
                  ),
                ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    index = 1;
                    setState(() {});
                  },
                  child: _buildTypeItem1(1, LocaleKeys.toys.tr),
                ),
              ),
            ],
          ),
        ),
        Center(
          child: Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: Colors.white.withOpacity(0.5),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTypeItem1(int idx, String title) {
    bool isSelected = index == idx;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(),
        Text(
          title,
          style: isSelected
              ? ITStyle.l5sem(const Color(0xff906bf7)) //
              : ITStyle.l5reg(const Color(0xff727374)),
        ),
        const SizedBox(height: 12),
        Container(
          height: 2,
          color: isSelected ? const Color(0xff906bf7) : Colors.transparent,
        )
      ],
    );
  }
}
