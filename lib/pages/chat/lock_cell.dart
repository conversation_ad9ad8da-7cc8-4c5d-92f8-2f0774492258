import 'dart:ui';

import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/event_util.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LockCell extends StatelessWidget {
  const LockCell({super.key, this.onTap});

  final void Function()? onTap;

  void _unLockTextGems() async {
    logEvent('c_news_locktext');
    if (!UserHelper().isVip.value) {
      IRouter.pushVip(VipSource.locktext);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.only(bottom: 16, end: 80),
      child: GestureDetector(
        onTap: _unLockTextGems,
        child: Stack(
          alignment: Alignment.topLeft,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 10.0),
              child: _buildContainer(),
            ),
            _buildLabel(),
          ],
        ),
      ),
    );
  }

  Widget _buildContainer() {
    return Container(
      height: 82,
      decoration: const BoxDecoration(
        color: IColor.black5,
        borderRadius: BorderRadiusDirectional.only(
          topEnd: Radius.circular(16),
          bottomEnd: Radius.circular(16),
          bottomStart: Radius.circular(16),
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.0),
            child: Text(
              'This is a introductionThis is ais a This is a This is aintroductionThis is ais a aintroductionThis is ais This is ais a ... ',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                child: Container(
                  height: double.infinity,
                  width: double.infinity,
                  color: Colors.black.withOpacity(0.9),
                ),
              ),
            ),
          ),
          Positioned.fill(
            child: _buildLock(),
          ),
        ],
      ),
    );
  }

  Widget _buildLock() {
    return Column(
      children: [
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(LocaleKeys.tap_to_see.tr, style: ITStyle.l6reg(IColor.n10)),
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
              decoration: BoxDecoration(
                color: const Color(0xffFFA942),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                LocaleKeys.message.tr,
                style: ITStyle.l6sem(const Color(0xff1C1C1C)),
              ),
            )
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: IColor.brand,
              ),
              child: Row(
                children: [
                  Assets.images.newUi.bananer.image(width: 24),
                  const Text(
                    'Unlock',
                    style: TextStyle(
                      color: Color(0xff1C1C1C),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLabel() {
    if (!ICache().isBusy) {
      return const SizedBox(
        width: 22,
        height: 22,
      );
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 20,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: const Color(0xffFFA942),
          ),
          child: Text(
            LocaleKeys.unlock_text_reply.tr,
            style: ITStyle.l7reg(const Color(0xff333333)),
          ),
        ),
      ],
    );
  }
}
