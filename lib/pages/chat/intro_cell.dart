import 'package:bushy/common/s_blur_background.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

class IntroCell extends StatelessWidget {
  const IntroCell({
    super.key,
    required this.title,
    required this.text,
  });

  final String title;
  final String text;

  @override
  Widget build(BuildContext context) {
    return SBlurBackground(
      blur: 46,
      backgroundColor: Colors.white.withOpacity(0.2),
      padding: const EdgeInsets.all(12),
      borderRadius: const BorderRadiusDirectional.only(
        topEnd: Radius.circular(16),
        bottomStart: Radius.circular(16),
        bottomEnd: Radius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: ITStyle.l4reg(IColor.brand)),
          const SizedBox(height: 2),
          Text(text, style: ITStyle.l4reg(Colors.white)),
        ],
      ),
    );
  }
}
