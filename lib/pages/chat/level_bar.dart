import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/s_level_progress.dart';
import '../../gen/assets.gen.dart';
import '../../other/i_ext.dart';
import '../../other/i_theme.dart';
import 'msg_ctr.dart';

class LevelBar extends StatelessWidget {
  const LevelBar({super.key});

  @override
  Widget build(BuildContext context) {
    MsgCtr ctr = Get.find();

    return Obx(() {
      final data = ctr.chatLevel.value;

      var level = data?.level ?? 1;

      // final map = ctr.chatLevelConfigs.firstWhereOrNull((element) => element['level'] == level);
      // var levelStr = map?['icon'] as String?;

      var progress = (data?.progress ?? 0) / 100.0;
      var rewards = '+${data?.rewards ?? 0}';

      var value = formatNumber(data?.progress);
      // var total = data?.upgradeRequirements?.toInt() ?? 0;
      // var proText = '$value/$total';

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Assets.images.newUi.gold.image(width: 16),
                Text(rewards, style: ITStyle.l6sem(Colors.white)),
                const SizedBox(width: 8),
                SLevelProgress(progress: progress),
              ],
            ),
            Text(
              'Lv $level $value%',
              style: ITStyle.l4reg(Colors.white),
            ),
          ],
        ),
      );
    });
  }
}
