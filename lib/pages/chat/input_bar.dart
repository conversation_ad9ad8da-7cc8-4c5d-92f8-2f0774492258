import 'dart:ui';

import 'package:bushy/data/input_btn_model.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/event_util.dart';
import 'package:bushy/other/i_ext.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/svg_icon.dart';
import 'package:bushy/pages/chat/gift_list.dart';
import 'package:bushy/pages/chat/input_actions.dart';
import 'package:bushy/pages/chat/msg_ctr.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

class InputBar extends StatefulWidget {
  const InputBar({super.key});

  @override
  State<InputBar> createState() => _InputBarState();
}

class _InputBarState extends State<InputBar> {
  final ctr = Get.find<MsgCtr>();

  late TextEditingController textEditingController;
  bool isSend = false;
  final FocusNode focusNode = FocusNode();

  late GiftList giftView;

  @override
  void initState() {
    super.initState();
    textEditingController = TextEditingController();
    textEditingController.addListener(_onInputChange);
    focusNode.addListener(onFocusChange);
  }

  @override
  void dispose() {
    super.dispose();
    textEditingController.removeListener(_onInputChange);
    focusNode.dispose();
    focusNode.removeListener(onFocusChange);
  }

  void _onInputChange() {
    isSend = textEditingController.text.isNotEmpty;
    setState(() {});
  }

  void onFocusChange() {}

  void onTapTag(int index) {
    final item = ctr.inputTags[index];
    final type = item.type;

    switch (type) {
      case InputBtnType.tease:
        final tagsString = LocaleKeys.msg_input_tags.tr;
        final tagList = tagsString.split('\n');
        textEditingController.text = tagList.randomOrNull ?? '';
        onSend();
        break;
      case InputBtnType.gifts:
        showGifts();
        break;
      default:
        SmartDialog.showToast('Not support');
    }
  }

  void showGifts() {
    Get.bottomSheet(
      GiftList(role: ctr.role, ctr: ctr),
      isScrollControlled: true,
      isDismissible: true,
    );
  }

  void onSend() async {
    focusNode.unfocus();
    String content = textEditingController.text.trim();
    if (content.isNotEmpty) {
      ctr.sendMsg(content);
      textEditingController.clear();
    } else {
      SmartDialog.showToast(LocaleKeys.please_enter_something.tr);
    }
    logEvent('c_chat_send');
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InputActions(
          tags: ctr.inputTags,
          onTap: onTapTag,
        ),
        const SizedBox(height: 12),
        Stack(
          children: [
            SafeArea(
              top: false,
              left: false,
              right: false,
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 48, sigmaY: 48),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                            color: Colors.white.withOpacity(0.2),
                            child: Row(
                              children: [
                                _buildSpecialButton(),
                                Expanded(child: _buildTextField()),
                                _buildSend(canSend: isSend),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () {
                            IRouter.pushMsgCall(sessionId: ctr.sessionId ?? 0, role: ctr.role);
                          },
                          child: Center(child: SvgIcon(assetName: Assets.images.newUi.phone)),
                        ))
                  ],
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _buildTextField() {
    return TextField(
      textInputAction: TextInputAction.send,
      onEditingComplete: onSend,
      cursorColor: IColor.brand,
      minLines: 1,
      maxLines: null,
      maxLength: 500, // 不显示计数器
      buildCounter: (BuildContext context, {int? currentLength, int? maxLength, bool? isFocused}) {
        return Container(); // 不显示计数器
      },
      style: const TextStyle(
        height: 1,
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      controller: textEditingController,
      decoration: InputDecoration(
        hintText: LocaleKeys.type_here.tr,
        hintStyle: ITStyle.l4reg(IColor.n05),
        fillColor: Colors.transparent,
        border: InputBorder.none,
        filled: true,
        isDense: true,
        contentPadding: const EdgeInsets.only(left: 5, top: 6, right: 5, bottom: 6),
      ),
      autofocus: false,
      focusNode: focusNode,
    );
  }

  Widget _buildSend({required bool canSend}) {
    return InkWell(
      onTap: canSend ? onSend : null,
      child: canSend
          ? SvgIcon(assetName: Assets.images.newUi.send)
          : SvgIcon(
              assetName: Assets.images.newUi.send,
              color: Colors.white.withOpacity(0.2),
            ),
    );
  }

  Widget _buildSpecialButton() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(focusNode);

        final text = textEditingController.text;
        final selection = textEditingController.selection;

        // Insert "**" at the current cursor position
        final newText = text.replaceRange(
          selection.start,
          selection.end,
          '**',
        );

        // Update the text and set the cursor between the two asterisks
        textEditingController.value = TextEditingValue(
          text: newText,
          selection: TextSelection.fromPosition(
            TextPosition(offset: selection.start + 1),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.only(top: 4),
        width: 20,
        height: 32,
        child: const Center(
          child: Text(
            '*',
            style: TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
