import 'package:bushy/common/s_image.dart';
import 'package:bushy/data/msg.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/event_util.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'text_cell.dart';

class VideoCell extends StatelessWidget {
  const VideoCell({super.key, required this.msg});

  final Msg msg;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.centerStart,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextCell(msg: msg),
          const SizedBox(height: 8),
          _buildImageWidget(),
        ],
      ),
    );
  }

  Widget _buildImageWidget() {
    var imageUrl = msg.thumbnailUrl ?? '';
    var isLockImage = msg.mediaLock == LockLevel.private.value;
    var imageWidth = 208 / 1.4;
    var imageHeight = 312 / 1.4;

    var videoUrl = msg.videoUrl ?? '';

    var imageWidget = ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: SImage(
        url: imageUrl,
        width: imageWidth,
        height: imageHeight,
        borderRadius: BorderRadius.circular(16),
      ),
    );

    var isHide = !UserHelper().isVip.value && isLockImage;

    return isHide ? _buildCover(imageWidth, imageHeight) : _buildVideoButton(videoUrl, imageWidget);
  }

  Widget _buildCover(double imageWidth, double imageHeight) {
    return GestureDetector(
      onTap: _onTapUnlock,
      child: Container(
        width: imageWidth,
        height: imageHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned.fill(child: Assets.images.newUi.videoBg.image(fit: BoxFit.cover)),
            Container(color: IColor.black8),
            Assets.images.newUi.play.image(width: 32),
            PositionedDirectional(
              top: 0,
              end: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 24,
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadiusDirectional.only(
                        topEnd: Radius.circular(16),
                        bottomStart: Radius.circular(16),
                      ),
                      color: Color(0xffFFA942),
                    ),
                    child: Center(
                      child: Text(
                        LocaleKeys.hot_video.tr,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoButton(String videoUrl, ClipRRect imageWidget) {
    return InkWell(
      onTap: () {
        IRouter.pushVideoPreview(videoUrl);
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          imageWidget,
          Assets.images.newUi.playBlack.image(width: 32),
        ],
      ),
    );
  }

  void _onTapUnlock() async {
    logEvent('c_news_lockvideo');
    final isVip = UserHelper().isVip.value;
    if (!isVip) {
      IRouter.pushVip(VipSource.lockpic);
    }
  }
}
