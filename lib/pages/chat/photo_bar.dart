import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../data/chater.dart';
import '../../other/i_cache.dart';
import '../../other/i_theme.dart';
import 'msg_ctr.dart';
import 'photo_cell.dart';

class PhotoBar extends StatefulWidget {
  const PhotoBar({super.key, required this.expandChaged});

  final void Function(double height) expandChaged;

  @override
  State<PhotoBar> createState() => _PhotoBarState();
}

class _PhotoBarState extends State<PhotoBar> {
  final imageHeight = 64.0;
  final buttonHeight = 14.0;

  bool _isExpanded = true;

  final ctr = Get.find<MsgCtr>();

  RxList<RoleImage> images = <RoleImage>[].obs;

  @override
  void initState() {
    super.initState();

    images.value = ctr.role.images ?? [];

    ever(ctr.roleImagesChaned, (_) {
      images.value = ctr.role.images ?? [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(() {
          return _buildImages();
        })
      ],
    );
  }

  Widget _buildImages() {
    final imageCount = images.length;

    if (!ICache().isBusy || imageCount == 0 || images.isEmpty) {
      return Container(height: 1, color: IColor.white2);
    }

    return Column(
      children: [
        _buildList(imageCount),
        Container(height: 1, color: IColor.black5),
        _buildExpand(),
      ],
    );
  }

  Widget _buildExpand() {
    return InkWell(
      splashColor: Colors.transparent, // 去除水波纹
      highlightColor: Colors.transparent, // 去除点击高亮
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
        widget.expandChaged.call(buttonHeight + 24 + 1 + (_isExpanded ? imageHeight + 12 : 0));
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Container(
          width: 26,
          height: buttonHeight,
          decoration: BoxDecoration(
            color: IColor.white1,
            borderRadius: BorderRadius.circular(7),
          ),
          child: Center(
            child: Icon(
              color: Colors.white,
              _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              size: 13,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildList(int imageCount) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300), // 动画持续时间
      curve: Curves.easeInOut, // 动画曲线
      margin: EdgeInsets.only(bottom: _isExpanded ? 12 : 0),
      height: _isExpanded ? 64 : 0, // 根据状态动态调整高度
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        itemBuilder: (_, idx) {
          final image = images[idx];
          final unlocked = image.unlocked ?? false;
          return _buildItem(image, unlocked);
        },
        separatorBuilder: (context, index) {
          return const SizedBox(width: 12);
        },
        itemCount: imageCount,
      ),
    );
  }

  Widget _buildItem(RoleImage image, bool unlocked) {
    return PhotoCell(
      image: image,
      onTapPic: () => ctr.onTapImage(image),
      onTap: () => ctr.onTapUnlockImage(image),
      unlocked: unlocked,
      imageHeight: imageHeight,
    );
  }
}
