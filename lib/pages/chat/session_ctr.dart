import 'package:bushy/common/i_empty.dart';
import 'package:bushy/data/session.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/service/i_p.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionCtr extends GetxController {
  var list = <Session>[].obs;

  int page = 1;
  int size = 100;
  var type = Rx<EmptyType?>(EmptyType.loading);
  bool isNoMoreData = false;

  final EasyRefreshController refCtr = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  @override
  void onInit() {
    super.onInit();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      refCtr.callRefresh();
    });
  }

  @override
  void dispose() {
    refCtr.dispose();
    super.dispose();
  }

  Future<void> onRefresh() async {
    page = 1;
    await fetchData();

    await Future.delayed(Duration(milliseconds: 50));
    refCtr.finishRefresh();
    refCtr.resetFooter();
  }

  Future<void> onLoad() async {
    page++;
    await fetchData();

    await Future.delayed(Duration(milliseconds: 50));
    refCtr.finishLoad(isNoMoreData ? IndicatorResult.noMore : IndicatorResult.none);
  }

  Future<void> fetchData() async {
    try {
      final res = await IP.sessionList(page, size);
      isNoMoreData = (res?.records?.length ?? 0) < size;
      if (page == 1) {
        list.clear();
      }
      list.addAll(res?.records ?? []);
      type.value = list.isEmpty ? EmptyType.chat : null;
    } catch (e) {
      type.value = list.isEmpty ? EmptyType.chat : null;
      if (page > 1) page--;
    }
  }

  Future<void> onItemTap(Session session) async {
    IRouter.pushChat(session.characterId);
  }
}
