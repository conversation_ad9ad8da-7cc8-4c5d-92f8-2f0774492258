import 'dart:async';
import 'dart:math';

import 'package:bushy/common/i_dialog.dart';
import 'package:bushy/common/language_checker.dart';
import 'package:bushy/common/s_button.dart';
import 'package:bushy/data/clothing_data.dart';
import 'package:bushy/data/gift.dart';
import 'package:bushy/data/input_btn_model.dart';
import 'package:bushy/data/msg.dart';
import 'package:bushy/data/msg_anser_level.dart';
import 'package:bushy/data/session.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/event_util.dart';
import 'package:bushy/other/i_cache.dart';
import 'package:bushy/other/i_config.dart';
import 'package:bushy/other/i_download.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/log_util.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:bushy/pages/chat/follow_ctr.dart';
import 'package:bushy/pages/chat/session_ctr.dart';
import 'package:bushy/service/i_p.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../data/chater.dart';
import '../../other/i_sse.dart';

class MsgCtr extends GetxController {
  var list = <Msg>[].obs;

  RxList<InputBtnModel> inputTags = <InputBtnModel>[].obs;

  late Chater role;
  late Session session;
  late int? sessionId;

  bool isNewChat = false;

  // 相册变动
  var roleImagesChaned = 0.obs;

  // 聊天等级变动
  Rx<MsgAnserLevel?> chatLevel = Rx<MsgAnserLevel?>(null);

  List<Map<String, dynamic>> chatLevelConfigs = [];

  // 发送id
  var tmpSendId = '987557965';
  Msg? tmpSendMsg;

  // 显示文字流的临时消息 id
  var tempId = '';

  bool isRecieving = false; // 正在接收消息
  bool isMediaStarted = false; // json 解析中
  bool isLock = false; // 是否加密

  final kTagNormal = 'TEXT-LOCK:NORMAL';
  final kTagPrivate = 'TEXT-LOCK:PRIVATE';

  StreamSubscription<String>? subscription;
  StringBuffer buffer = StringBuffer();

  String? inputLang;

  @override
  void onInit() {
    super.onInit();

    // 获取传递的参数
    var arguments = Get.arguments;
    if (arguments != null) {
      role = arguments['role'];
      session = arguments['session'];
      sessionId = session.id;
    }

    setupTease();

    loadMsg();

    _loadChatLevel();

    loadGifts();
  }

  @override
  void onClose() {
    log.d('onClose');
    closeSSE();
    subscription?.cancel();
    subscription = null;
    super.onClose();
  }

  void loadGifts() async {
    IConfig().loadGifts();
  }

  Future loadMsg() async {
    if (sessionId == null) {
      return;
    }
    list.clear();
    _addDefaaultTips();
    final page = await IP.messageList(1, 10000, sessionId!);
    if (page != null) {
      final records = page.records ?? [];
      for (var msg in records) {
        msg.showTranslate = msg.translateAnswer?.isNotEmpty ?? false;
      }
      list.addAll(records);
    }
  }

  void _addDefaaultTips() {
    final tips = Msg();
    tips.source = MsgSource.tips;
    list.add(tips);

    if (role.scenario != null && role.scenario!.isNotEmpty) {
      final intro = Msg();
      intro.source = MsgSource.scenario;
      list.add(intro);
    } else {
      if (role.aboutMe != null && role.aboutMe!.isNotEmpty) {
        final intro = Msg();
        intro.source = MsgSource.intro;
        list.add(intro);
      }
    }
    _addRandomGreetings();
  }

  Future<void> _addRandomGreetings() async {
    final greetings = role.greetings;
    final greetingsVoices = role.greetingsVoice;
    if (greetings == null || greetings.isEmpty) {
      return;
    }
    int randomIndex = Random().nextInt(greetings.length);
    var str = greetings[randomIndex];

    String? voiceUrl;
    int voiceDur = 0;
    if (greetingsVoices != null && greetingsVoices.length > randomIndex) {
      final voice = greetingsVoices[randomIndex];
      voiceUrl = voice.url;
      voiceDur = voice.duration ?? 0;

      if (sessionId != null) {
        final isExist = ICache().isSessionExist(sessionId!);
        if (isExist) {
          isNewChat = false;
          log.d('------旧会话');
        } else {
          log.d('------新会话');
          isNewChat = true;
          if (voiceUrl != null && voiceUrl.isNotEmpty) {
            IDownload.download(voiceUrl);
          }
          ICache().addSessionId(sessionId!);
        }
      }
    }
    final msg = Msg();
    msg.id = '${DateTime.now().millisecondsSinceEpoch}';
    msg.answer = str;
    msg.voiceUrl = voiceUrl;
    msg.voiceDur = voiceDur;
    msg.source = MsgSource.welcome;
    list.add(msg);
  }

  void setupTease() {
    inputTags.clear();
    if (!ICache().isBusy) {
      return;
    }
    inputTags.add(InputBtnModel.tease());
    inputTags.add(InputBtnModel.gifts());

    // final count = CacheTool().sendMsgCount;
    // if (count >= CacheTool().showClothingCount) {
    //   inputTags.add(
    //     {
    //       'id': 1,
    //       'name': LocaleKeys.undre.tr,
    //       'icon': Assets.images.icUndr.path,
    //       'list': [],
    //     },
    //   );
    // }
  }

  Future<bool> canSendMsg(String text) async {
    if (!ICache().isBusy && ICache().isBlocked(role.id)) {
      SmartDialog.showToast('You have blocked this person and cannot chat with him');
      return false;
    }
    if (isRecieving) {
      SmartDialog.showToast(LocaleKeys.wait_for_response.tr);
      return false;
    }

    Msg lastMsg = list.last;
    if (lastMsg.typewriterAnimated) {
      SmartDialog.showToast(LocaleKeys.wait_for_response.tr);
      return false;
    }

    if (text.isEmpty) {
      SmartDialog.showToast(LocaleKeys.please_enter_content.tr);
      return false;
    }
    final roleId = role.id;
    if (roleId == null) {
      return false;
    }
    if (!UserHelper().isVip.value) {
      if (role.gems == true) {
        final flag = UserHelper().isBalanceEnough(ConsumeSource.text);
        if (!flag) {
          await SmartDialog.showToast(LocaleKeys.gold_not_enough.tr);
          IRouter.pushGem(ConsumeSource.send);
          return false;
        }
      } else {
        /// 免费角色 - 最大免费条数
        final maxCount = IConfig().maxFreeChatCount;
        final sencCount = ICache().mc;

        if (sencCount > maxCount) {
          _showFreeChatCreditsUsed();
          return false;
        }
      }
    }
    return true;
  }

  void _showFreeChatCreditsUsed() {
    IDialog.sheet(
      Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 50),
            child: Text(
              LocaleKeys.free_chat_credits_used.tr,
              textAlign: TextAlign.center,
              style: ITStyle.l4reg(IColor.n10),
            ),
          ),
          const SizedBox(height: 32),
          SButton(
            title: LocaleKeys.confirm.tr,
            onTap: () {
              SmartDialog.dismiss();
              logEvent('t_chat_send');
              IRouter.pushVip(VipSource.send);
            },
            type: SButtonType.primary,
          ),
          const SizedBox(height: 8),
          SButton(
            title: LocaleKeys.cancel.tr,
            onTap: () {
              SmartDialog.dismiss();
            },
            type: SButtonType.secondary,
          ),
        ],
      ),
    );
  }

  void checkSendCount() {
    // 发送成功后，更新发送次数
    ICache().mc++;
    // setupTease();

    // if (ICache().isBusy) {
    //   var count = ICache().mc;
    //   if (count == ICache().showClothingCount) {
    //     AppDialog.alert(
    //       message: LocaleKeys.easter_egg_unlock.tr,
    //       confirmText: LocaleKeys.yes.tr,
    //       onConfirm: () {
    //         SmartDialog.dismiss();
    //         RouterTool.pushUndr(role);
    //       },
    //     );
    //   }
    // }

    if (ICache().mc == 8) {
      IDialog.showRateUs(LocaleKeys.rate_us_message.tr);
    }
  }

  Future<bool> resetConv() async {
    SmartDialog.showLoading();
    var result = await IP.resetSession(sessionId ?? 0);
    SmartDialog.dismiss();
    if (result != null) {
      list.clear();
      _addDefaaultTips();
      return true;
    }
    return false;
  }

  Future<bool> deleteConv() async {
    SmartDialog.showLoading();
    var result = await IP.deleteSession(sessionId ?? 0);
    ICache().removeSessionId(sessionId ?? 0);
    Get.find<SessionCtr>().onRefresh();
    Get.find<FollowCtr>().onRefresh();
    SmartDialog.dismiss();
    return result;
  }

  Future sendMsg(String text) async {
    bool canSend = await canSendMsg(text);
    if (!canSend) {
      return;
    }
    isRecieving = true;

    final charId = role.id;
    final conversationId = sessionId ?? 0;
    final uid = UserHelper().user?.id;
    if (charId == null || uid == null) {
      return;
    }

    // 临时发送显示的消息
    final msg = Msg(
      id: tmpSendId,
      question: text,
      userId: UserHelper().user?.id,
      conversationId: conversationId,
      characterId: charId,
      onAnswer: true,
    );
    msg.source = MsgSource.sendText;
    list.add(msg);
    tmpSendMsg = msg;

    // 确保流监听不重复
    await subscription?.cancel();
    subscription = null;

    startListening();

    inputLang = await LanguageChecker().detectLanguage(text);

    ISSE().sendMessage(charId, conversationId, uid, text);
  }

  void startListening() {
    try {
      if (subscription != null) {
        log.d("已有监听器，跳过 startListening");
        return;
      }

      isLock = false;
      tempId = DateTime.now().millisecondsSinceEpoch.toString();
      Msg currentMsg = Msg(id: tempId, answer: buffer.toString());

      log.d("开始监听...");
      subscription = ISSE().stream.listen(
        (data) async {
          log.d('Received SSE data: $data');
          progressSSE(data, currentMsg);
        },
        onError: (e) {
          log.e('------->Error: $e');
          closeSSE();
        },
        onDone: () {
          log.d('Stream completed');
          closeSSE();
        },
        cancelOnError: true,
      );
    } catch (e) {
      log.e('------->Error: $e');
    }
  }

  void progressSSE(String data, Msg currentMsg) async {
    if (data.contains(kTagNormal)) {
      isLock = false;
    } else if (data.contains(kTagPrivate)) {
      isLock = true;
    }

    // 去掉换行符
    data = data.replaceAll(RegExp(r'[\r\n]+'), '');

    if (data.contains('Insufficient gold')) {
      log.d('EOF Insufficient gold');
      list.removeLast();
      closeSSE();
      IRouter.pushGem(ConsumeSource.send);
      return;
    }

    if (data.contains('EOF')) {
      log.d('EOF received, clearing buffer and stopping listening');
      closeSSE();
      return;
    }

    if (data.contains('MEDIA START')) {
      isMediaStarted = true;

      /// 修改发送消息的状态
      tmpSendMsg?.onAnswer = false;

      final regex = RegExp(r'MEDIA START(.*?)MEDIA END', dotAll: true);
      final match = regex.firstMatch(data);

      if (match != null) {
        String jsonString = match.group(1)!.trim();
        log.d('Extracted JSON: $jsonString');
        Msg msg = Msg.fromRawJson(jsonString);
        await autoTranslateMsg(msg);

        if (msg.conversationId == sessionId) {
          msg.onAnswer = true;
          if (isLock) {
            msg.typewriterAnimated = UserHelper().isVip.value;
          } else {
            msg.typewriterAnimated = true;
          }

          final index = list.indexOf(currentMsg);
          log.d('currentMsg index: $index');
          if (index != -1) {
            list[index] = msg;
          } else {
            list.add(msg);
          }
          checkSendCount();

          _checkChatLevel(msg);
        }
      } else {
        log.e('No match found for MEDIA START');
      }
      isRecieving = false;
      await UserHelper().getUserInfo();
    }
  }

  void closeSSE() async {
    buffer.clear();
    isRecieving = false;
    isMediaStarted = false;

    log.d("关闭监听...");
    await subscription?.cancel();
    subscription = null;
    log.d("监听已关闭");
  }

  void _checkChatLevel(Msg msg) async {
    bool upgrade = msg.upgrade ?? false;
    int rewards = msg.rewards ?? 0;
    MsgAnserLevel? level = msg.appUserChatLevel;
    chatLevel.value = level;
    if (upgrade) {
      // 升级了
      await _showChatLevelUp(rewards);

      if ((level?.level ?? 0) == 3) {
        if (IDialog.rateLevel3Shoed == false) {
          IDialog.showRateUs(LocaleKeys.rate_us_message.tr);
          IDialog.rateLevel3Shoed = true;
        }
      }
    }
  }

  Future _showChatLevelUp(int rewards) async {
    await IDialog.showChatLevelUp(rewards);
  }

  void _loadChatLevel() async {
    try {
      var configs = await IConfig().reqCLC();
      chatLevelConfigs = configs.isEmpty
          ? IConfig().levelList
          : configs.map((c) {
              return {
                'icon': c.title ?? '👋',
                'level': c.level ?? 1,
                'text': LocaleKeys.level_up_value.trParams({
                  'level': '${c.level}',
                  'reward': '${c.reward}',
                }),
              };
            }).toList();

      final roleId = role.id;
      final userId = UserHelper().user?.id;
      if (roleId == null || userId == null) {
        return;
      }
      var res = await IP.fetchChatLevel(charId: roleId, userId: userId);
      chatLevel.value = res;
    } catch (e) {
      log.e('loadChatLevel is error:$e');
    }
  }

  Future<void> onTapUnlockImage(RoleImage image) async {
    final gems = image.gemTally ?? 0;
    if (UserHelper().balance.value < gems) {
      IRouter.pushGem(ConsumeSource.album);
      return;
    }

    final imageId = image.id;
    final modelId = image.modelId;
    if (imageId == null || modelId == null) {
      return;
    }

    SmartDialog.showLoading();
    final res = await IP.unlockImageReq(imageId, modelId);
    SmartDialog.dismiss();
    if (res) {
      // 创建一个新的 images 列表
      final updatedImages = role.images?.map((i) {
        if (i.id == imageId) {
          return i.copyWith(unlocked: true);
        }
        return i;
      }).toList();

      // 更新 Role 对象
      role = role.copyWith(images: updatedImages);
      roleImagesChaned.value++;
      UserHelper().getUserInfo();

      onTapImage(image);
    }
  }

  void onTapImage(RoleImage image) {
    final imageUrl = image.imageUrl;
    if (imageUrl == null) {
      return;
    }
    IRouter.pushImagePreview(imageUrl);
  }

  Future<void> autoTranslateMsg(Msg msg) async {
    if (msg.translateAnswer != null) {
      return;
    }
    final content = msg.answer?.trim();

    final id = msg.id;
    if (id == null) return;
    if (id == tmpSendId) return;
    // 内容为空直接返回
    if (content == null || content.isEmpty) return;

    final languageCode = inputLang ?? ICache().locale?.languageCode ?? 'en';

    var isLanguage = await LanguageChecker().isLanguage(content, languageCode);
    if (isLanguage) {
      return;
    }
    final result = await IP.translateText(content, languageCode);
    if (result != null) {
      msg.translateAnswer = result;
      IP.saveTranslateMsg(id: id, translateAnswer: result);
    }
  }

  // void translateMsg(Msg msg) async {
  //   Msg lastMsg = list.last;
  //   if (lastMsg.typewriterAnimated) {
  //     SmartDialog.showToast(LocaleKeys.wait_for_response.tr);
  //     return;
  //   }

  //   final content = msg.answer;

  //   // 内容为空直接返回
  //   if (content == null || content.isEmpty) return;

  //   // 定义更新消息的方法
  //   Future<void> updateMessage({
  //     required bool showTranslate,
  //     String? translate,
  //   }) async {
  //     msg.showTranslate = showTranslate;

  //     if (translate != null) {
  //       msg.translateAnswer = translate;
  //     }
  //     list.refresh();
  //   }

  //   // 根据状态处理逻辑
  //   if (msg.showTranslate == true) {
  //     await updateMessage(showTranslate: false);
  //   } else if (msg.translateAnswer != null) {
  //     await updateMessage(showTranslate: true);
  //     TransManager().handleTranslationClick();
  //   } else {
  //     logEvent('c_trans');
  //     if (msg.translateAnswer == null) {
  //       // 获取翻译内容
  //       SmartDialog.showLoading();
  //       String? result = await IP.translateText(content);
  //       final id = msg.id;
  //       if (result != null && id != null) {
  //         IP.saveTranslateMsg(id: id, translateAnswer: result);
  //       }
  //       SmartDialog.dismiss();
  //       // 更新消息并显示翻译
  //       await updateMessage(showTranslate: true, translate: result);
  //     } else {
  //       await updateMessage(showTranslate: true);
  //     }

  //     TransManager().handleTranslationClick();
  //   }
  // }

  void closeGiftSheet() async {
    if (Get.isBottomSheetOpen == true) {
      Get.back();
    }
  }

  void sendGift(Gift gift) async {
    final convId = session.id;
    final giftId = gift.id;
    final roleId = role.id;
    if (convId == null || giftId == null || roleId == null) {
      return;
    }
    closeGiftSheet();

    Msg? msg = await IP.sendGift(convId: convId, giftId: giftId, roleId: roleId);
    if (msg != null) {
      await autoTranslateMsg(msg);
      list.add(msg);
    }
    UserHelper().getUserInfo();
  }

  void sendClothing(ClothingData clothing) async {
    final convId = session.id;
    final clothingId = clothing.id;
    final roleId = role.id;
    if (convId == null || clothingId == null || roleId == null) {
      return;
    }
    isRecieving = true;

    closeGiftSheet();

    IDialog.showMsgClotheLoading();

    SmartDialog.showLoading();
    Msg? msg = await IP.sendClothes(convId: convId, clothingId: clothingId, roleId: roleId);
    if (msg != null) {
      await autoTranslateMsg(msg);
    }
    SmartDialog.dismiss();

    UserHelper().getUserInfo();

    var imgUrl = msg?.giftImg;
    log.d('imgUrl: $imgUrl');
    if (imgUrl != null) {
      try {
        // 创建 Completer 来等待图片加载完成
        Completer<void> completer = Completer<void>();

        final ExtendedNetworkImageProvider imageProvider = ExtendedNetworkImageProvider(imgUrl, cache: true);

        imageProvider.resolve(const ImageConfiguration()).addListener(
              ImageStreamListener(
                (ImageInfo image, bool synchronousCall) {
                  // 图片加载完成
                  log.d('图片加载完成');
                  // 图片加载完成后调用 completer.complete()，表示图片加载已完成
                  if (!completer.isCompleted) {
                    completer.complete();
                  }
                },
                onChunk: (ImageChunkEvent event) {
                  // 图片加载进度
                  log.d('Loading progress: ${event.cumulativeBytesLoaded / (event.expectedTotalBytes ?? 1)}');
                },
                onError: (dynamic exception, StackTrace? stackTrace) {
                  // 图片加载失败
                  log.e('图片加载失败: $exception');
                  if (!completer.isCompleted) {
                    completer.completeError(exception); // 加载失败时返回异常
                  }
                },
              ),
            );
        // 等待图片加载完成
        await completer.future;
      } catch (e) {
        print(e);
      }
    }

    IDialog.hiddenMsgClotheLoading();

    isRecieving = false;

    if (msg != null) {
      list.add(msg);

      IRouter.pushImagePreview(imgUrl ?? '');
    } else {
      SmartDialog.showToast(LocaleKeys.some_error_try_again.tr);
    }
  }
}
