import 'package:bushy/common/s_image.dart';
import 'package:bushy/data/msg.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/pages/chat/msg_ctr.dart';
import 'package:bushy/pages/chat/text_cell.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GiftCell extends StatefulWidget {
  const GiftCell({super.key, required this.msg});

  final Msg msg;

  @override
  State<GiftCell> createState() => _GiftCellState();
}

class _GiftCellState extends State<GiftCell> {
  MsgCtr ctr = Get.find<MsgCtr>();

  @override
  Widget build(BuildContext context) {
    final question = widget.msg.translateQuestion ?? widget.msg.question ?? '';

    return Column(
      children: [
        _buildQuestion(question),
        const SizedBox(height: 16),
        TextCell(msg: widget.msg),
      ],
    );
  }

  Widget _buildQuestion(String question) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          decoration: const BoxDecoration(
            color: IColor.brand,
            borderRadius: BorderRadiusDirectional.only(
              topStart: Radius.circular(16),
              bottomStart: Radius.circular(16),
              topEnd: Radius.circular(16),
            ),
          ),
          padding: const EdgeInsets.all(12),
          width: 184,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: InkWell(
                  onTap: () {
                    IRouter.pushImagePreview(widget.msg.giftImg ?? '');
                  },
                  child: Container(
                    height: 213,
                    color: Colors.white,
                    child: SImage(url: widget.msg.giftImg),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                question,
                style: ITStyle.l4reg(IColor.black),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
