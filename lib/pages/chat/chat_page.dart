import 'package:bushy/common/keep_alive_wrapper.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/nav_obs.dart';
import 'package:bushy/pages/chat/follow_ctr.dart';
import 'package:bushy/pages/chat/follow_view.dart';
import 'package:bushy/pages/chat/session_ctr.dart';
import 'package:bushy/pages/chat/session_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with SingleTickerProviderStateMixin, RouteAware {
  late TabController _tabController;

  List<String> tabTitles = [];
  List<Widget> tabContents = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    NavObs().routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didPopNext() {
    super.didPopNext();

    Get.find<SessionCtr>().onRefresh();
    Get.find<FollowCtr>().onRefresh();
  }

  @override
  void initState() {
    super.initState();

    tabContents = [
      KeepAliveWrapper(child: SessionView()),
      KeepAliveWrapper(child: FollowView()),
    ];

    _tabController = TabController(length: tabContents.length, vsync: this);

    _tabController.addListener(() {
      if (_tabController.index != _tabController.previousIndex) {
        setState(() {}); // Trigger UI update when tab changes
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    tabTitles = [
      LocaleKeys.chatted.tr,
      LocaleKeys.liked.tr,
    ];

    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: IColor.brandjb,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            children: [
              const SizedBox(height: kToolbarHeight),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: buildTab(
                        tabTitles[0],
                        0,
                        Assets.images.newUi.tabChatD.image(width: 24),
                        Assets.images.newUi.tabChatS.image(width: 24),
                      ),
                    ),
                    Expanded(
                      child: buildTab(
                        tabTitles[1],
                        1,
                        Assets.images.newUi.tabLikeD.image(width: 24),
                        Assets.images.newUi.tabLikeS.image(width: 24),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 1,
                color: IColor.n05,
              ),
            ],
          ),
        ),
      ),
      body: TabBarView(controller: _tabController, children: tabContents),
    );
  }

  // Custom tab button
  Widget buildTab(String title, int index, Widget icon, Widget chooseIcon) {
    bool isSelected = _tabController.index == index;

    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () => _tabController.animateTo(index),
      child: Column(
        children: [
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                isSelected ? chooseIcon : icon,
                const SizedBox(width: 6),
                Text(
                  title,
                  style: isSelected ? ITStyle.l4sem(IColor.brand) : ITStyle.l4reg(IColor.n03),
                ),
              ],
            ),
          ),
          Container(
            height: 2,
            color: isSelected ? IColor.brand : Colors.transparent,
          )
        ],
      ),
    );
  }
}
