import 'package:bushy/data/msg.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';

class SendCell extends StatelessWidget {
  const SendCell({super.key, required this.msg});

  final isSend = false;
  final Msg msg;

  @override
  Widget build(BuildContext context) {
    final sendText = msg.question;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Align(
              alignment: AlignmentDirectional.centerEnd,
              child: Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                spacing: 8,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    constraints: BoxConstraints(maxWidth: Get.width * 0.8),
                    decoration: const BoxDecoration(
                      color: Color(0xff906BF7),
                      borderRadius: BorderRadiusDirectional.only(
                        topStart: Radius.circular(16),
                        bottomStart: Radius.circular(16),
                        topEnd: Radius.circular(16),
                      ),
                    ),
                    child: Text(
                      sendText ?? '',
                      style: ITStyle.l4reg(Colors.white),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
        if (msg.onAnswer == true)
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: 64,
                height: 32,
                margin: const EdgeInsets.only(top: 16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                ),
                child: Center(
                    child: LoadingIndicator(
                  indicatorType: Indicator.ballPulse,
                  colors: const [Colors.white],
                  strokeWidth: 2,
                )),
              ),
            ],
          ),
      ],
    );
  }
}
