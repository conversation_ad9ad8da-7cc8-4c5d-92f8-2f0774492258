import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../common/keep_alive_wrapper.dart';
import '../../data/msg.dart';
import '../../generated/locales.g.dart';
import '../../other/i_cache.dart';
import '../../other/i_enum.dart';
import 'audio_cell.dart';
import 'clothe_cell.dart';
import 'gift_cell.dart';
import 'image_cell.dart';
import 'intro_cell.dart';
import 'msg_ctr.dart';
import 'send_cell.dart';
import 'text_cell.dart';
import 'tips_cell.dart';
import 'video_cell.dart';

class MsgTableView extends StatefulWidget {
  const MsgTableView({super.key});

  @override
  State<MsgTableView> createState() => _MsgTableViewState();
}

class _MsgTableViewState extends State<MsgTableView> {
  late AutoScrollController autoController;

  MsgCtr ctr = Get.find<MsgCtr>();

  @override
  void initState() {
    super.initState();
    double msgBottom = ICache().isBusy ? 92 : 56;

    autoController = AutoScrollController(
      viewportBoundaryGetter: () => Rect.fromLTRB(0, 0, 0, msgBottom),
      axis: Axis.vertical,
    );
  }

  @override
  Widget build(BuildContext context) {
    final listHeight = MediaQuery.of(context).size.height - MediaQuery.of(context).padding.bottom - 20;
    final top = listHeight / 5;

    return GestureDetector(
      onPanDown: (_) {
        try {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (currentFocus.focusedChild != null && !currentFocus.hasPrimaryFocus) {
            FocusManager.instance.primaryFocus?.unfocus();
          } else {
            SystemChannels.textInput.invokeMethod('TextInput.hide');
          }
        } catch (e) {}
      },
      child: ShaderMask(
        blendMode: BlendMode.dstIn,
        shaderCallback: (rect) {
          return const LinearGradient(
            colors: [Colors.transparent, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0, 0.2],
          ).createShader(rect);
        },
        child: Obx(() {
          final list = ctr.list.reversed.toList();
          return ListView.separated(
            controller: autoController,
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(top: top, bottom: 20),
            reverse: true,
            itemBuilder: (context, index) {
              var item = list[index];
              return AutoScrollTag(
                controller: autoController,
                index: index,
                key: ValueKey(index),
                highlightColor: Colors.black.withOpacity(0.1),
                child: _buildItem(item),
              );
            },
            separatorBuilder: (context, index) {
              return const SizedBox(height: 16);
            },
            itemCount: list.length,
          );
        }),
      ),
    );
  }

  Widget _buildItem(Msg msg) {
    final source = msg.source;

    // 定义不同 MsgSource 的组件映射
    final widgetBuilders = <MsgSource, Widget Function()>{
      MsgSource.tips: () => const TipsCell(),
      MsgSource.scenario: () => IntroCell(
            title: LocaleKeys.scenario.tr,
            text: ctr.role.scenario ?? '',
          ),
      MsgSource.intro: () => IntroCell(
            title: LocaleKeys.intro.tr,
            text: ctr.role.aboutMe ?? '',
          ),
      MsgSource.sendText: () => SendCell(msg: msg),
      MsgSource.text: () => TextCell(msg: msg),
      MsgSource.welcome: () => TextCell(msg: msg),
      MsgSource.photo: () => ImageCell(msg: msg),
      MsgSource.video: () => KeepAliveWrapper(child: VideoCell(msg: msg)),
      MsgSource.audio: () => AudioCell(msg: msg),
      MsgSource.gift: () => GiftCell(msg: msg),
      MsgSource.clothe: () => ClotheCell(msg: msg),
    };

    return widgetBuilders[source]?.call() ?? Container();
  }
}
