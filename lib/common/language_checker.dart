import 'package:flutter/foundation.dart';
import 'package:google_mlkit_language_id/google_mlkit_language_id.dart';

class LanguageChecker {
  LanguageChecker._privateConstructor();
  static final LanguageChecker _instance = LanguageChecker._privateConstructor();
  factory LanguageChecker() => _instance;

  // confidenceThreshold: 0.5，表示只有在置信度大于等于 50% 时才返回语言识别结果。如需更严格或更宽松的判断，可调整该数值（范围 0.0 ~ 1.0）。
  final LanguageIdentifier _languageIdentifier = LanguageIdentifier(confidenceThreshold: 0.8);

  /// 检测文本语言代码（如 'en'、'zh'、'de'）
  Future<String?> detectLanguage(String text) async {
    try {
      final languageCode = await _languageIdentifier.identifyLanguage(text);
      if (languageCode == 'und') {
        return null; // undetected
      }
      return languageCode;
    } catch (e) {
      return null; // undetected
    }
  }

  /// 判断字符串是否为目标语言
  Future<bool> isLanguage(String text, String targetLanguageCode) async {
    final detectedLang = await detectLanguage(text);
    var isLanguage = detectedLang?.toLowerCase() == targetLanguageCode.toLowerCase();
    debugPrint(
        '[🌐Language🌐]text: $text, detectedLang: $detectedLang, targetLang: $targetLanguageCode, isLanguage: $isLanguage');
    return isLanguage;
  }

  void dispose() {
    _languageIdentifier.close();
  }
}
