import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

class SLevelProgress extends StatefulWidget {
  final double progress; // 当前进度，0.0 - 1.0
  final double width;
  final double height;
  final List<Color> gradientColors; // 渐变色列表
  final Color trackColor; // 轨道颜色
  final double borderRadius; // 圆角半径
  final Duration animationDuration; // 动画时长

  const SLevelProgress({
    super.key,
    required this.progress,
    this.width = 160,
    this.height = 6,
    this.gradientColors = IColor.brandjb, // 默认渐变色
    this.trackColor = const Color(0xff727374), // 默认轨道颜色
    this.borderRadius = 3.0,
    this.animationDuration = const Duration(milliseconds: 300), // 默认动画时长
  }) : assert(progress >= 0.0 && progress <= 1.0, 'Progress must be between 0.0 and 1.0');

  @override
  State<SLevelProgress> createState() => _SLevelProgressState();
}

class _SLevelProgressState extends State<SLevelProgress> {
  late double _currentProgress;

  @override
  void initState() {
    super.initState();
    _currentProgress = widget.progress; // 初始化当前进度
  }

  @override
  void didUpdateWidget(covariant SLevelProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.progress != oldWidget.progress) {
      // 如果 progress 值发生变化，更新状态
      setState(() {
        _currentProgress = widget.progress;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Column(
          children: [
            Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                color: widget.trackColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
              child: Directionality(
                textDirection: TextDirection.rtl, // 设置从右到左的方向
                child: Stack(
                  children: [
                    AnimatedContainer(
                      duration: widget.animationDuration,
                      curve: Curves.easeInOut,
                      width: widget.width * _currentProgress,
                      height: widget.height,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: widget.gradientColors,
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(widget.borderRadius),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
