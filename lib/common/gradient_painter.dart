import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

class GradientPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..shader = const LinearGradient(
        colors: IColor.brandjb,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    Path path = Path();
    path.lineTo(0, size.height); // 左侧下降
    path.quadraticBezierTo(0, size.height - 16, 16, size.height - 16);
    path.lineTo(size.width - 16, size.height - 16);
    path.quadraticBezierTo(size.width, size.height - 16, size.width, size.height - 16 - 16); // 右侧向上
    path.lineTo(size.width, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
