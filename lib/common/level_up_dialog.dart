import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:lottie/lottie.dart';

class LlLUpDialog extends StatefulWidget {
  const LlLUpDialog({super.key});

  @override
  State<LlLUpDialog> createState() => _LlLUpDialogState();
}

class _LlLUpDialogState extends State<LlLUpDialog> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    // 初始化 AnimationController
    _controller = AnimationController(vsync: this);

    // 监听动画状态
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 动画完成时的处理逻辑
        SmartDialog.dismiss();
      }
    });

    Future.delayed(const Duration(milliseconds: 500), () {
      // 显示动画
      _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(
        'assets/lottie/level_up.json',
        controller: _controller,
        onLoaded: (composition) {
          // 设置动画时长
          _controller.duration = composition.duration;
        },
      ),
    );
  }
}
