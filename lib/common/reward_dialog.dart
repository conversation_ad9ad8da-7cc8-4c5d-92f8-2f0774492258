import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_router.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:bushy/service/i_p.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

class RewardDialog extends StatelessWidget {
  const RewardDialog({super.key});

  void onTopCollect() async {
    await IP.getDailyReward();
    UserHelper().getUserInfo();
    SmartDialog.dismiss(tag: DialogTag.sigin.name);
  }

  void onTapVip() {
    IRouter.pushVip(VipSource.dailyrd);
  }

  @override
  Widget build(BuildContext context) {
    var isVip = UserHelper().isVip.value;

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: Get.width - 60,
            decoration: BoxDecoration(
              color: const Color(0xff906BF7),
              borderRadius: BorderRadius.circular(16),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 30),
            padding: const EdgeInsets.symmetric(vertical: 24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  LocaleKeys.daily_reward.tr,
                  style: ITStyle.l2sem(Colors.white),
                ),
                const SizedBox(height: 20),
                Assets.images.newUi.gold3.image(width: 140, height: 105),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      isVip ? '+50' : '+20',
                      style: ITStyle.l1sem(Colors.white),
                    ),
                    const SizedBox(width: 8),
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'coins',
                        style: ITStyle.l5reg(Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          isVip ? _buildVip() : _buildNormal(),
          const SizedBox(height: 24),
          GestureDetector(
            child: Assets.images.newUi.closeWhite.image(width: 20),
            onTap: () {
              SmartDialog.dismiss(tag: DialogTag.sigin.name);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildVip() {
    return InkWell(
      onTap: onTopCollect,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Container(
        height: 40,
        constraints: const BoxConstraints(minWidth: 120),
        margin: const EdgeInsets.symmetric(horizontal: 30),
        decoration: BoxDecoration(
          color: const Color(0x80906BF7),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            LocaleKeys.collect.tr,
            style: ITStyle.l3sem(Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildNormal() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: onTopCollect,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            child: Container(
              height: 40,
              constraints: const BoxConstraints(minWidth: 120),
              decoration: BoxDecoration(
                color: const Color(0x80906BF7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  LocaleKeys.collect.tr,
                  style: ITStyle.l3sem(Colors.white),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              children: [
                InkWell(
                  onTap: onTapVip,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  child: Container(
                    height: 40,
                    constraints: const BoxConstraints(minWidth: 120),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(colors: IColor.brandjb),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Center(
                      child: Text(
                        LocaleKeys.go_to_pro.tr,
                        style: ITStyle.l3sem(Colors.black),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      LocaleKeys.pro_50.tr,
                      style: ITStyle.l5reg(Colors.white),
                    ),
                    const SizedBox(width: 2),
                    Assets.images.newUi.gold.image(width: 16),
                    const SizedBox(width: 2),
                    Text(
                      "/${LocaleKeys.day.tr}",
                      style: ITStyle.l5reg(Colors.white),
                    ),
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
