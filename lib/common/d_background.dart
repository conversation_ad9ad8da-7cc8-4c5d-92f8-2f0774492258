import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/other/svg_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

class DBackground extends StatelessWidget {
  const DBackground({
    super.key,
    required this.child,
    this.isRealHeight = false,
  });

  final Widget child;
  final bool? isRealHeight;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (isRealHeight == false) const Spacer(),
        SizedBox(
          width: double.infinity,
          child: SheetBackground(child: child),
        ),
      ],
    );
  }
}

class SheetBackground extends StatelessWidget {
  const SheetBackground({
    super.key,
    required this.child,
    this.onClose,
  });

  final Widget child;
  final Function()? onClose;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
      ),
      child: Container(
        color: Colors.white,
        child: Stack(
          children: [
            Column(
              children: [
                const SizedBox(height: 24),
                child,
                const SizedBox(height: 24),
                <PERSON><PERSON><PERSON><PERSON>(height: MediaQuery.of(context).padding.bottom)
              ],
            ),
            PositionedDirectional(
              top: 0,
              end: 0,
              child: InkWell(
                onTap: () {
                  onClose != null ? onClose?.call() : SmartDialog.dismiss();
                },
                child: Padding(
                  padding: const EdgeInsets.all(18.0),
                  child: SvgIcon(
                    assetName: Assets.images.newUi.svgClose,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
