import 'dart:ui';

import 'package:bushy/common/gradient_text.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

class ChooseBtn extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const ChooseBtn({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  // 常量
  static const double _buttonHeight = 35.0;
  static const double _borderRadius = 8.0;
  static const EdgeInsets _padding = EdgeInsets.all(1.0);

  // 创建基础按钮样式的 Container
  Widget _buildButtonContainer(Widget child, {bool? isChoosed = false}) {
    return Padding(
      padding: _padding,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(_borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
          child: Container(
            height: _buttonHeight,
            decoration: BoxDecoration(
              color: IColor.black5,
              borderRadius: BorderRadius.circular(_borderRadius),
              border: Border.all(
                color: isChoosed == true ? IColor.brand : Colors.transparent,
                width: 1.0,
              ),
            ),
            child: Center(child: child),
          ),
        ),
      ),
    );
  }

  // 默认按钮样式
  Widget _buildDefaultButton() {
    return _buildButtonContainer(
      Text(
        title,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: ITStyle.l5reg(IColor.n10),
      ),
    );
  }

  // 选中按钮样式
  Widget _buildSelectedButton() {
    return _buildButtonContainer(
      GradientText(
        data: title,
        gradient: const LinearGradient(
          colors: IColor.brandjb,
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        style: ITStyle.l5sem(Colors.white),
      ),
      isChoosed: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(_borderRadius),
      child: isSelected ? _buildSelectedButton() : _buildDefaultButton(),
    );
  }
}
