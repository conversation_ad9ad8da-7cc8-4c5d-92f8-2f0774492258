import 'package:flutter/material.dart';

class SProgress extends StatefulWidget {
  final Duration duration;
  final double width;
  final double height;
  final Color backgroundColor;
  final Color progressColor;
  final double borderRadius;
  final Function(double) onProgress;

  const SProgress({
    super.key,
    required this.duration,
    required this.width,
    required this.height,
    required this.backgroundColor,
    required this.progressColor,
    required this.borderRadius,
    required this.onProgress,
  });

  @override
  State<SProgress> createState() => _SProgressState();
}

class _SProgressState extends State<SProgress> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    )
      ..addListener(() {
        widget.onProgress(_controller.value);
      })
      ..forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: Align(
            alignment: AlignmentDirectional.centerStart,
            child: FractionallySizedBox(
              widthFactor: _controller.value,
              child: Container(
                height: widget.height,
                decoration: BoxDecoration(
                  color: widget.progressColor,
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
