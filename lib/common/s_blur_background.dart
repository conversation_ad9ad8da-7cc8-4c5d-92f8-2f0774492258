import 'dart:ui';

import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

class SBlurBackground extends StatelessWidget {
  const SBlurBackground({
    super.key,
    required this.blur,
    this.backgroundColor = IColor.white2,
    this.radius = 16.0,
    this.child,
    this.padding,
    this.borderRadius,
    this.constraints,
  });

  final double blur;
  final Color backgroundColor;
  final double radius;
  final Widget? child;
  final EdgeInsetsGeometry? padding;
  final BorderRadiusGeometry? borderRadius;
  final BoxConstraints? constraints;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(radius),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          padding: padding,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: borderRadius ?? BorderRadius.circular(radius),
          ),
          constraints: constraints,
          child: child,
        ),
      ),
    );
  }
}
