import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class IEmpty extends StatelessWidget {
  const IEmpty({
    super.key,
    required this.type,
    this.hintText,
    this.image,
    this.physics,
    this.size,
    this.loadingIconColor,
    this.onReload,
  });

  final EmptyType type;
  final String? hintText;
  final Widget? image;
  final Size? size;
  final ScrollPhysics? physics;
  final Color? loadingIconColor;
  final void Function()? onReload;

  @override
  Widget build(BuildContext context) {
    List<Widget> widgets = [];

    widgets.add(type.image());

    widgets.add(const SizedBox(height: 16));

    String hint = type.text();
    widgets.add(Text(
      hintText ?? hint,
      style: const TextStyle(
        fontSize: 14,
        color: Color(0xFFB3B3B3),
        fontWeight: FontWeight.w500,
        fontStyle: FontStyle.italic,
      ),
      textAlign: TextAlign.center,
    ));

    if (type == EmptyType.noNetwork && onReload != null) {
      widgets.add(SizedBox(height: 16.w));
      widgets.add(GestureDetector(
        behavior: HitTestBehavior.translucent,
        child: Container(
          width: 81,
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.w), color: Colors.black),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25.w),
                  color: IColor.brand,
                ),
              ),
              Text(
                LocaleKeys.reload.tr,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        onTap: () {
          onReload?.call();
        },
      ));
    }

    Widget createContent(double width, double height) {
      return ListView(
        padding: EdgeInsets.zero,
        physics: physics ?? const NeverScrollableScrollPhysics(),
        children: [
          const SizedBox(height: 156),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: widgets,
          ),
        ],
      );
    }

    return size != null ? createContent(size!.width, size!.height) : createContent(Get.width, Get.height);
  }
}

enum EmptyType {
  // 加载中
  loading,
  // 空
  empty,
  //无网络
  noNetwork,
  search,
  chat,
}

extension EmptyTypeExt on EmptyType {
  // 提供 image 方法，支持自定义宽度和高度，并设置默认宽高
  Widget image({double width = 200, double height = 200}) {
    switch (this) {
      case EmptyType.loading:
        return Assets.images.newUi.loading.image(width: width, height: height);

      case EmptyType.empty:
        return Assets.images.newUi.noChat.image(width: width, height: height);

      case EmptyType.noNetwork:
        return Assets.images.newUi.noNet.image(width: width, height: height);

      case EmptyType.search:
        return Assets.images.newUi.noSearchResult.image(width: width, height: height);

      default:
        return Assets.images.newUi.noChat.image(width: width, height: height);
    }
  }

  String text() {
    switch (this) {
      case EmptyType.loading:
        return LocaleKeys.loading.tr;
      case EmptyType.empty:
        return LocaleKeys.no_data.tr;
      case EmptyType.chat:
        return LocaleKeys.no_chat.tr;
      case EmptyType.search:
        return LocaleKeys.no_bushys_here_yet.tr;
      case EmptyType.noNetwork:
        return LocaleKeys.no_network_connection.tr;
      default:
        return '';
    }
  }
}
