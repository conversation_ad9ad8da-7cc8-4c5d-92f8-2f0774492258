import 'dart:io';

import 'package:bushy/common/s_button.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/info_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:url_launcher/url_launcher.dart';

class RateDialog extends StatelessWidget {
  const RateDialog({super.key, required this.msg});

  final String msg;

  void close() {
    SmartDialog.dismiss(tag: 'rate_app_dialog');
  }

  static void _showError(String message) {
    SmartDialog.showNotify(msg: message, notifyType: NotifyType.error);
  }

  static Future<void> openAppStoreReview() async {
    if (Platform.isIOS) {
      const String appId = '6743832770'; // App Store的应用程序ID
      final Uri url = Uri.parse('https://apps.apple.com/app/id$appId?action=write-review');

      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        _showError('Could not launch $url');
      }
    } else if (Platform.isAndroid) {
      String packageName = await InfoHeper().packageName(); // 你的Android应用包名
      final Uri url = Uri.parse('https://play.google.com/store/apps/details?id=$packageName');

      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        _showError('Could not launch $url');
      }
    } else {
      _showError('Unsupported platform');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Spacer(),
        Stack(
          alignment: AlignmentDirectional.topEnd,
          children: [
            Image.asset('assets/images/new_ui/rate_img.png', width: 140),
            Container(
              margin: EdgeInsets.only(top: 90),
              padding: EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                image: DecorationImage(
                  image: AssetImage('assets/images/new_ui/rate_bg.png'),
                  fit: BoxFit.cover,
                ),
              ),
              child: Column(
                children: [
                  SizedBox(height: 24),
                  Text(
                    LocaleKeys.help_us_grow.tr,
                    style: ITStyle.l3sem(IColor.n02),
                  ),
                  SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      msg,
                      style: ITStyle.l4reg(IColor.n02),
                    ),
                  ),
                  SizedBox(height: 26),
                  SButton(
                    title: '${LocaleKeys.help_app.tr} Fantasy Role',
                    type: SButtonType.primary,
                    onTap: openAppStoreReview,
                    margin: EdgeInsets.symmetric(horizontal: 0),
                  ),
                  SizedBox(height: 12),
                  SButton(
                    title: LocaleKeys.nope.tr,
                    type: SButtonType.secondary,
                    onTap: close,
                    margin: EdgeInsets.symmetric(horizontal: 0),
                  ),
                  SizedBox(height: 32),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
