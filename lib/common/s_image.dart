import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';

class SImage extends StatelessWidget {
  const SImage({
    super.key,
    this.url,
    this.width,
    this.height,
    this.shape,
    this.border,
    this.borderRadius,
    this.bgColor,
  });

  final String? url;
  final double? width;
  final double? height;
  final BoxShape? shape;
  final BoxBorder? border;
  final BorderRadius? borderRadius;
  final Color? bgColor;

  @override
  Widget build(BuildContext context) {
    if (url == null || url!.isEmpty) {
      return Container(
        width: width,
        height: height,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: borderRadius,
          border: border,
          color: bgColor ?? IColor.n06,
        ),
        child: Assets.images.newUi.noNet.image(
          width: width,
          height: height,
          fit: BoxFit.cover,
        ),
      );
    }

    return ExtendedImage.network(
      url!,
      width: width,
      height: height,
      fit: BoxFit.cover,
      borderRadius: borderRadius,
      shape: shape ?? BoxShape.rectangle,
      border: border,
      loadStateChanged: (state) {
        switch (state.extendedImageLoadState) {
          case LoadState.loading:
            return Container(
              color: const Color(0x33906BF7),
              child: Center(
                child: Icon(
                  Icons.image,
                  color: Colors.grey.withOpacity(0.5),
                ),
              ),
            );
          case LoadState.failed:
            return Container(
              color: const Color(0x33906BF7),
              child: Center(
                child: Assets.images.newUi.noNet.image(
                  width: MediaQuery.of(context).size.width / 4,
                  height: MediaQuery.of(context).size.width / 4,
                ),
              ),
            );

          case LoadState.completed:
            break;
        }
        return null;
      },
    );
  }
}
