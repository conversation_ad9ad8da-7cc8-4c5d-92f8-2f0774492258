import 'package:bushy/common/s_image.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

import '../data/chater.dart';

class RoleAnaRow extends StatelessWidget {
  const RoleAnaRow({super.key, required this.role});

  final Chater role;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 24,
          height: 24,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.all(1),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: IColor.brandjb,
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: SImage(
                width: 24,
                height: 24,
                url: role.avatar,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            role.name ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: ITStyle.l4sem(IColor.n10),
          ),
        ),
        const SizedBox(width: 4),
        Container(
          height: 18,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Center(
            child: Text(
              '${role.age ?? 0}',
              style: ITStyle.l6reg(IColor.n10),
            ),
          ),
        ),
      ],
    );
  }
}
