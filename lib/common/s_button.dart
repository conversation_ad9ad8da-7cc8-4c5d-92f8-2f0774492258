import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

enum SButtonType {
  primary,
  secondary,
  delete,
  custom,
}

class SButton extends StatelessWidget {
  const SButton({
    super.key,
    this.onTap,
    required this.title,
    required this.type,
    this.margin = const EdgeInsets.symmetric(horizontal: 60),
    this.child,
    this.bgColor,
    this.titleColor,
  });

  final void Function()? onTap;
  final String title;
  final SButtonType type;
  final EdgeInsetsGeometry? margin;
  final Widget? child;
  final Color? bgColor;
  final Color? titleColor;

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case SButtonType.primary:
        return _buildPrimary();
      case SButtonType.secondary:
        return _buildSecondary();
      case SButtonType.delete:
        return _buildDelete();
      case SButtonType.custom:
        return _buildCustom();
      default:
        return _buildPrimary();
    }
  }

  Widget _buildCustom() {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(18)),
        color: bgColor,
      ),
      child: InkWell(
        onTap: onTap,
        child: Center(
          child: child ??
              Text(
                title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: ITStyle.l4sem(titleColor ?? IColor.n02),
              ),
        ),
      ),
    );
  }

  Widget _buildDelete() {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      margin: margin,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(18)),
        color: Colors.white,
      ),
      child: InkWell(
        onTap: onTap,
        child: Center(
          child: Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: ITStyle.l4sem(IColor.orther03),
          ),
        ),
      ),
    );
  }

  Widget _buildPrimary() {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      margin: margin,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(18)),
        color: IColor.brand,
      ),
      child: InkWell(
        onTap: onTap,
        child: Center(
          child: child ??
              Text(
                title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: ITStyle.l4sem(IColor.n10),
              ),
        ),
      ),
    );
  }

  Widget _buildSecondary() {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 36,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        margin: margin,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(18)),
          color: Color(0xffC9C9C9),
        ),
        child: Center(
          child: Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: ITStyle.l4sem(IColor.n10),
          ),
        ),
      ),
    );
  }
}
