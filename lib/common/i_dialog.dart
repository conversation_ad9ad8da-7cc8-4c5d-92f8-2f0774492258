import 'dart:async';

import 'package:bushy/common/d_background.dart';
import 'package:bushy/common/level_dialog.dart';
import 'package:bushy/common/level_up_dialog.dart';
import 'package:bushy/common/rate_dialog.dart';
import 'package:bushy/common/reward_dialog.dart';
import 'package:bushy/common/s_button.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_config.dart';
import 'package:bushy/other/i_enum.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:bushy/other/user_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';

class IDialog {
  static Future showRechargeSucc(String text) {
    return SmartDialog.show(
      clickMaskDismiss: true,
      maskColor: IColor.black8,
      keepSingle: true,
      tag: 'gold_succ',
      builder: (_) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 37),
              width: double.infinity,
              decoration: BoxDecoration(
                color: IColor.brand,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Assets.images.newUi.gold3.image(width: 199, height: 158),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        text,
                        style: ITStyle.l1sem(IColor.n10),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Text(
                          'coins',
                          style: ITStyle.l5reg(IColor.n10),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
            const SizedBox(height: 26),
            GestureDetector(
              child: Assets.images.newUi.closeWhite.image(width: 24),
              onTap: () => SmartDialog.dismiss(tag: 'gold_succ'),
            ),
          ],
        );
      },
    );
  }

  static Future alert({
    String? title,
    String? message,
    Widget? messageWidget,
    bool? clickMaskDismiss = true,
    String? cancelText,
    String? confirmText,
    void Function()? onCancel,
    void Function()? onConfirm,
  }) async {
    return SmartDialog.show(
      clickMaskDismiss: true,
      keepSingle: true,
      alignment: Alignment.bottomCenter,
      animationType: SmartAnimationType.fade,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.horizontal(left: Radius.circular(16)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 50),
                child: Text(
                  message ?? '',
                  textAlign: TextAlign.center,
                  style: ITStyle.l4reg(Colors.black),
                ),
              ),
              const SizedBox(height: 32),
              SButton(
                title: LocaleKeys.confirm.tr,
                onTap: onConfirm,
                type: SButtonType.primary,
              ),
              const SizedBox(height: 8),
              SButton(
                title: LocaleKeys.cancel.tr,
                onTap: () {
                  SmartDialog.dismiss();
                  onCancel?.call();
                },
                type: SButtonType.secondary,
              ),
              const SizedBox(height: 12),
            ],
          ),
        );
      },
    );
  }

  static Future sheet(Widget child) async {
    return SmartDialog.show(
      clickMaskDismiss: true,
      keepSingle: true,
      alignment: Alignment.bottomCenter,
      animationType: SmartAnimationType.fade,
      builder: (_) {
        return DBackground(
          child: child,
        );
      },
    );
  }

  static Future input({
    String? title,
    String? message,
    String? hintText,
    Widget? messageWidget,
    bool? clickMaskDismiss = false,
    String? cancelText,
    String? confirmText,
    void Function()? onCancel,
    void Function()? onConfirm,
    FocusNode? focusNode, // FocusNode 参数
    TextEditingController? textEditingController, // TextEditingController 参数
  }) async {
    final focusNode1 = focusNode ?? FocusNode();
    final textController1 = textEditingController ?? TextEditingController();

    return SmartDialog.show(
      clickMaskDismiss: true,
      useAnimation: false, // 关闭动画
      tag: DialogTag.nickname.name,
      maskColor: Colors.black.withOpacity(0.25),
      builder: (context) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 在渲染完成之后调用焦点请求，确保键盘弹出
          focusNode1.requestFocus();
        });

        double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

        return AnimatedPadding(
          duration: const Duration(milliseconds: 150),
          curve: Curves.easeOut,
          padding: EdgeInsets.only(bottom: keyboardHeight),
          child: Material(
            type: MaterialType.transparency,
            child: DBackground(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildText(title, 24, FontWeight.w700),
                  if (title?.isNotEmpty == true) const SizedBox(height: 16),
                  _buildText(message, 14, FontWeight.w500, color: Colors.black),
                  if (messageWidget != null) messageWidget,
                  const SizedBox(height: 16),
                  Container(
                    height: 36,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    margin: const EdgeInsets.symmetric(horizontal: 32),
                    decoration: BoxDecoration(
                      color: IColor.n08,
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Center(
                      child: TextField(
                        autofocus: false,
                        textInputAction: TextInputAction.done,
                        textAlign: TextAlign.center,
                        onEditingComplete: onConfirm,
                        minLines: 1,
                        maxLength: 20,
                        style: GoogleFonts.montserrat(
                          height: 1,
                          color: const Color(0xff2C2C2C),
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                        controller: textController1,
                        decoration: InputDecoration(
                          hintText: hintText ?? 'input',
                          counterText: '', // 去掉字数显示
                          hintStyle: const TextStyle(color: Color(0xffC9C9C9)),
                          fillColor: Colors.transparent,
                          border: InputBorder.none,
                          filled: true,
                          isDense: true,
                        ),
                        focusNode: focusNode1,
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 65),
                    child: _buildButton(
                      confirmText ?? LocaleKeys.confirm.tr,
                      IColor.brand,
                      Colors.white,
                      onConfirm,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static Future showLoginReward() async {
    if (SmartDialog.checkExist(tag: DialogTag.sigin.name)) {
      return;
    }
    return SmartDialog.show(
      tag: DialogTag.sigin.name,
      clickMaskDismiss: true,
      keepSingle: true,
      maskColor: Colors.black.withOpacity(0.7),
      builder: (BuildContext context) {
        return const Center(
          child: RewardDialog(),
        );
      },
    );
  }

  static Future showChatLevel() async {
    await IConfig().reqCLC();
    return sheet(const LlLDialog());
  }

  static bool _isChatLevelDialogVisible = false;

  static Future<void> showChatLevelUp(int rewards) async {
    // 防止重复弹出
    if (_isChatLevelDialogVisible) return;

    // 设置标记为显示中
    _isChatLevelDialogVisible = true;

    try {
      await _showLevelUpToast(rewards);
    } finally {
      _isChatLevelDialogVisible = false;
    }
  }

  static Future<void> _showLevelUpToast(int rewards) async {
    final toastMessage = LocaleKeys.level_up_toast.trParams({'rewards': rewards.toString()});

    final completer = Completer<void>();

    await SmartDialog.showToast(
      toastMessage,
      debounce: true,
      onDismiss: () async {
        await _showLevelUpDialog(rewards);
        completer.complete();
      },
    );
    await completer.future;
  }

  static Future<void> _showLevelUpDialog(int rewards) async {
    final completer = Completer<void>();

    await SmartDialog.show(
        tag: DialogTag.chatLevel.name,
        clickMaskDismiss: false,
        keepSingle: true,
        builder: (BuildContext context) => const LlLUpDialog(),
        onDismiss: () async {
          await UserHelper().getUserInfo();
          completer.complete();
        });
    await completer.future;
  }

  static Widget _buildText(
    String? text,
    double fontSize,
    FontWeight fontWeight, {
    Color color = Colors.black,
  }) {
    if (text?.isNotEmpty != true) return const SizedBox.shrink();
    return Text(
      text!,
      textAlign: TextAlign.center,
      style: TextStyle(
        color: color,
        fontSize: fontSize,
        fontWeight: fontWeight,
      ),
    );
  }

  static Widget _buildButton(
    String title,
    Color bgColor,
    Color textColor,
    void Function()? onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 32,
        margin: const EdgeInsets.symmetric(horizontal: 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: bgColor,
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  static Future hiddenMsgClotheLoading() {
    return SmartDialog.dismiss(tag: DialogTag.clotheLoading.name);
  }

  static Future showMsgClotheLoading() {
    return SmartDialog.show(
      tag: DialogTag.clotheLoading.name,
      keepSingle: true,
      alignment: Alignment.bottomCenter,
      animationType: SmartAnimationType.fade,
      clickMaskDismiss: false,
      builder: (BuildContext context) {
        return Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Lottie.asset(Assets.lottie.giftLoading, width: 36.0),
                const SizedBox(height: 16),
                Text(
                  LocaleKeys.sara_received_your_gift.tr,
                  style: ITStyle.l21sem(null),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    LocaleKeys.give_her_a_moment.tr,
                    style: ITStyle.l4reg(null),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  height: 1,
                  color: Colors.white.withOpacity(0.1),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    LocaleKeys.it_may_take_up_to_30_seconds.tr,
                    style: ITStyle.l5reg(null),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  static bool rateLevel3Shoed = false;

  static bool rateCollectShowd = false;

  static void showRateUs(String msg) async {
    SmartDialog.show(
      clickMaskDismiss: false,
      keepSingle: true,
      alignment: Alignment.bottomCenter,
      animationType: SmartAnimationType.fade,
      builder: (_) {
        return RateDialog(msg: msg);
      },
      tag: 'rate_app_dialog',
    );
  }
}
