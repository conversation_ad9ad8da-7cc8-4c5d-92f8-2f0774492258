import 'package:bushy/data/level_config.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/generated/locales.g.dart';
import 'package:bushy/other/i_config.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LlLDialog extends StatefulWidget {
  const LlLDialog({super.key});

  @override
  State<LlLDialog> createState() => _LlLDialogState();
}

class _LlLDialogState extends State<LlLDialog> {
  List<LevelConfig> configs = [];

  @override
  void initState() {
    super.initState();

    loadData();
  }

  Future<void> loadData() async {
    configs = await IConfig().reqCLC();

    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          LocaleKeys.level_up_intimacy.tr,
          style: ITStyle.l3sem(const Color(0xff906BF7)),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 18,
            crossAxisSpacing: 12,
            childAspectRatio: 148.0 / 112.0,
          ),
          itemBuilder: (BuildContext context, int index) {
            final config = configs[index];

            return Column(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: IColor.brand1,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          config.title ?? '👋',
                          style: ITStyle.l1sem(Colors.black),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Assets.images.newUi.gold.image(width: 18),
                            const SizedBox(width: 4),
                            Text(
                              '+${config.reward}',
                              style: ITStyle.l5sem(Colors.black),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  LocaleKeys.level_up_value.trParams({'level': '${config.level}'}),
                  style: ITStyle.l5reg(Colors.black),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            );
          },
          itemCount: configs.length,
        ),
      ],
    );
  }
}
