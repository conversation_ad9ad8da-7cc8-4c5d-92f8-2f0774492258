import 'package:flutter/material.dart';

class SSwitch extends StatefulWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;
  final Color activeColor;
  final Color thumbColor;
  final Color trackColor;

  const SSwitch({
    super.key,
    required this.value,
    this.onChanged,
    this.activeColor = const Color(0xff906bf7),
    this.thumbColor = const Color(0xFFFFFFFF),
    this.trackColor = const Color(0xFFB3B3B3),
  });

  @override
  State<SSwitch> createState() => _SSwitchState();
}

class _SSwitchState extends State<SSwitch> {
  @override
  Widget build(BuildContext context) {
    bool value = widget.value; // 直接使用传入的 value 控制外观

    return GestureDetector(
      onTap: () {
        setState(() {
          value = !value;
          if (widget.onChanged != null) {
            widget.onChanged!(value);
          }
        });
      },
      child: Container(
        width: 36,
        height: 20,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: value ? widget.activeColor : widget.trackColor,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            AnimatedPositioned(
              duration: const Duration(milliseconds: 200),
              left: value ? (36 - 16 - 2) : 2,
              right: value ? 2 : (36 - 16 - 2),
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.thumbColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
