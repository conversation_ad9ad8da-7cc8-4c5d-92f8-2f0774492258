import 'dart:ui';

import 'package:bushy/data/chater.dart';
import 'package:bushy/gen/assets.gen.dart';
import 'package:bushy/other/i_theme.dart';
import 'package:flutter/material.dart';

class FavoredButton extends StatelessWidget {
  const FavoredButton({
    super.key,
    required this.onCollect,
    required this.role,
  });

  final void Function(Chater role) onCollect;
  final Chater role;

  @override
  Widget build(BuildContext context) {
    bool isCollect = role.collect ?? false;

    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4), // 模糊效果
        child: InkWell(
          splashColor: Colors.transparent, // 去除水波纹
          highlightColor: Colors.transparent, // 去除点击高亮
          onTap: () {
            onCollect(role);
          },
          child: Container(
            height: 20,
            padding: const EdgeInsets.symmetric(horizontal: 6),
            decoration: const BoxDecoration(
              color: Color(0x801C1C1C),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                isCollect
                    ? Assets.images.newUi.followed.image(width: 12, height: 12)
                    : Assets.images.newUi.follow.image(width: 12, height: 12),
                const SizedBox(width: 2),
                Flexible(
                  child: Text(
                    '${role.likes ?? 0}',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: ITStyle.l6reg(IColor.n10),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
