import 'package:bushy/common/language_checker.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized(); // 👈 必须初始化

  final checker = LanguageChecker();

  final testCases = <Map<String, String>>[
    {'text': 'Hello, how are you?', 'lang': 'en'},
    {'text': 'مرحبا، كيف حالك اليوم؟', 'lang': 'ar'},
    {'text': 'Bonjour, comment allez-vous aujourd\'hui ?', 'lang': 'fr'},
    {'text': 'Hallo, wie geht es dir heute?', 'lang': 'de'},
    {'text': 'Ho<PERSON>, ¿cómo estás hoy?', 'lang': 'es'},
    {'text': 'Olá, como você está hoje?', 'lang': 'pt'},
    {'text': 'こんにちは、お元気ですか？今日はいい天気ですね。', 'lang': 'ja'},
    {'text': '안녕하세요, 오늘 기분이 어떠세요?', 'lang': 'ko'},
    {'text': '<PERSON>ia<PERSON>, come ti senti oggi?', 'lang': 'it'},
    {'text': 'Merhaba, bugün nasılsın?', 'lang': 'tr'},
    {'text': 'Xin chào, bạn cảm thấy thế nào hôm nay?', 'lang': 'vi'},
    {'text': 'Halo, bagaimana kabarmu hari ini?', 'lang': 'id'},
    {'text': 'สวัสดี, วันนี้คุณเป็นอย่างไรบ้าง?', 'lang': 'th'},
    {'text': 'Kamusta ka ngayon?', 'lang': 'fil'},
    {'text': '今天你好嗎？希望一切順利。', 'lang': 'zh'},
  ];

  group('Language detection tests', () {
    for (var testCase in testCases) {
      testWidgets('Detect language for: ${testCase['lang']}', (WidgetTester tester) async {
        final result = await checker.detectLanguage(testCase['text']!);
        expect(result?.isNotEmpty, true, reason: 'Language detection returned empty for input: ${testCase['text']}');

        final expectedLang = testCase['lang'];
        if (expectedLang == 'fil') {
          expect(result?.toLowerCase(), anyOf(['fil', 'tl']), reason: 'Expected fil (or tl), got $result');
        } else {
          expect(result?.toLowerCase(), expectedLang, reason: 'Expected $expectedLang, got $result');
        }
      });
    }

    testWidgets('Detect empty string returns und', (tester) async {
      final result = await checker.detectLanguage('');
      expect(result, 'und');
    });

    testWidgets('Detect gibberish returns und', (tester) async {
      final result = await checker.detectLanguage('asldkjasldkjasldkj');
      expect(result, 'und');
    });

    testWidgets('Portuguese fallback: pt-PT returns pt', (tester) async {
      final result = await checker.detectLanguage('Olá, como está o seu dia?');
      expect(result?.toLowerCase(), 'pt');
    });

    testWidgets('isLanguage returns true for correct match', (tester) async {
      final result = await checker.isLanguage('Hello world!', 'en');
      expect(result, true);
    });

    testWidgets('isLanguage returns false for incorrect match', (tester) async {
      final result = await checker.isLanguage('Bonjour tout le monde', 'en');
      expect(result, false);
    });
  });

  tearDownAll(() {
    checker.dispose();
  });
}
