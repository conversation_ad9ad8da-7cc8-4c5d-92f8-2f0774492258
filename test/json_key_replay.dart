import 'dart:io';

// 在终端中直接运行 main 方法：
/*
dart run /Users/<USER>/Documents/kira_ai/test/json_key_replay.dart
*/
/// 入口方法
void main() {
  // 指定文件夹路径（你的开发机上的绝对路径）
  const String folderPath = '/Users/<USER>/Documents/bushy/lib/data';

  // 调用替换方法
  replace(folderPath);
}

void replace(String folderPath) {
  // 替换规则
  final Map<String, String> replacementMap1 = {
    "duration": "dur",
    "platform": "platfrm",
    "adid": "ad_ident",
    "video_chat": "vid_chat",
    "currency_code": "curr_code",
    "gen_video": "video_generate",
    "gender": "gendr",
    "msg_id": "msg_identifier",
    "product_id": "prod_id",
    "about_me": "about_me_txt",
    "media": "media_content",
    "create_time": "creat_time",
    "user_id": "usr_id",
    "render_style": "rend_style",
    "session_count": "sess_count",
    "receipt": "recpt_info",
    "update_time": "upd_time",
    "name": "usr_name",
    "next_msgs": "next_messages",
    "avatar": "avtr_img",
    "order_num": "ord_num",
    "original_transaction_id": "orig_transact_id",
    "conv_id": "conv_ident",
    "source": "src_plat",
    "currency_symbol": "curr_symbl",
    "planned_msg_id": "planned_msg",
    "character_id": "char_id",
    "url": "web_link",
    "transaction_id": "transact_id",
    "voice_duration": "voice_dur",
    "age": "yr_age",
    "password": "passwd",
    "gen_photo_tags": "photo_tags",
    "subscription": "subscr_stat",
    "gems": "gem_bal",
    "signature": "signatr",
    "gen_photo": "photo_gen",
    "recharge_status": "recharge_stat",
    "idfa": "idf_a",
    "greetings": "greet_msg",
    "amount": "amt",
    "nick_name": "nicknm",
    "template_id": "tmpl_id",
    "choose_env": "env_choice",
    "greetings_voice": "greet_voice",
    "character_video_chat": "char_vid_chat",
    "shelving": "shelve_stat",
    "tags": "tag_list",
    "voice_url": "voice_link",
    "lock_level": "lock_lvl",
    "deserved_gems": "earned_gems",
    "order_type": "ord_type",
    "price": "item_price",
    "hide_character": "hide_char",
    "thumbnail_url": "thumb_link",
    "device_id": "dev_id",
    "character_name": "char_name",
    "lock_level_media": "lock_lvl_media",
    "voice_id": "voice_ident",
    "vip": "vip_status",
    "token": "auth_token",
    "likes": "like_cnt",
    "chat": "chat_session",
    "engine": "engn",
  };

  final Map<String, String> newMap = {
    "dur": "dur",
    "platfrm": "plt",
    "ad_ident": "adv_id",
    "vid_chat": "vchat",
    "currency_code": "crncy_cd",
    "video_generate": "gen_vid",
    "gendr": "gndr",
    "msg_identifier": "mid",
    "prod_id": "prod_id",
    "about_me_txt": "bio",
    "media_content": "med",
    "creat_time": "crt_tm",
    "usr_id": "uid",
    "rend_style": "rnd_stl",
    "sess_count": "sess_cnt",
    "recpt_info": "rcpt",
    "upd_time": "upd_tm",
    "usr_name": "nm",
    "next_messages": "nxt_msg",
    "avtr_img": "avt",
    "ord_num": "ord_no",
    "orig_transact_id": "orig_txn",
    "conv_ident": "cid",
    "src_plat": "src",
    "curr_symbl": "curr_sym",
    "planned_msg": "pln_msgid",
    "char_id": "char_id",
    "web_link": "uri",
    "transact_id": "txn_id",
    "voice_dur": "vox_dur",
    "yr_age": "yrs",
    "passwd": "pwd",
    "photo_tags": "gen_ptags",
    "subscr_stat": "sub",
    "gem_bal": "gms",
    "signatr": "sig",
    "photo_gen": "gen_ph",
    "recharge_stat": "rc_stat",
    "idf_a": "advid",
    "greet_msg": "grt",
    "amt": "amt",
    "nicknm": "nick",
    "tmpl_id": "tpl_id",
    "env_choice": "env_sel",
    "greet_voice": "gr_voi",
    "char_vid_chat": "char_vchat",
    "shelve_stat": "shlv",
    "tag_list": "tg",
    "voice_link": "vox_url",
    "lock_lvl": "lck_lvl",
    "earned_gems": "due_gms",
    "ord_type": "ord_typ",
    "item_price": "prc",
    "hide_char": "hid_char",
    "thumb_link": "thmb_url",
    "dev_id": "dev_id",
    "char_name": "char_nm",
    "lock_lvl_media": "lck_med",
    "voice_ident": "vid",
    "vip_status": "vip_lvl",
    "auth_token": "tkn",
    "like_cnt": "lk",
    "chat_session": "cht",
    "engn": "eng",
  };

  // 获取文件夹
  final Directory directory = Directory(folderPath);
  if (!directory.existsSync()) {
    print('文件夹不存在: $folderPath');
    return;
  }
  final List<FileSystemEntity> files = directory.listSync();

  for (final FileSystemEntity entity in files) {
    if (entity is File) {
      String fileContent = entity.readAsStringSync();

      // 使用正则替换内容
      final RegExp regex = RegExp(r'"([^"]*)"');
      final String replacedContent = regex.allMatches(fileContent).fold(
        fileContent,
        (String content, Match match) {
          final String key = match.group(1)!;
          if (newMap.containsKey(key)) {
            return content.replaceFirst(match[0]!, '"${newMap[key]}"');
          }
          return content;
        },
      );

      entity.writeAsStringSync(replacedContent);
      print('文件已成功替换: ${entity.path}');
    }
  }
}

var m1 = {
  "lk": "likes",
  "nm": "name",
  "tg": "tags",
  "amt": "amount",
  "avt": "avatar",
  "bio": "about_me",
  "cht": "chat",
  "cid": "conv_id",
  "dur": "duration",
  "eng": "engine",
  "gms": "gems",
  "grt": "greetings",
  "med": "media",
  "mid": "msg_id",
  "msg": "message",
  "plt": "platform",
  "prc": "price",
  "pwd": "password",
  "sig": "signature",
  "src": "source",
  "sub": "subscription",
  "tkn": "token",
  "uid": "user_id",
  "uri": "url",
  "vid": "voice_id",
  "yrs": "age",
  "gndr": "gender",
  "nick": "nick_name",
  "rcpt": "receipt",
  "shlv": "shelving",
  "advid": "idfa",
  "parts": "images",
  "vchat": "video_chat",
  "adv_id": "adid",
  "crt_tm": "create_time",
  "dev_id": "device_id",
  "gen_ph": "gen_photo",
  "gr_voi": "greetings_voice",
  "ord_no": "order_num",
  "tpl_id": "template_id",
  "txn_id": "transaction_id",
  "upd_tm": "update_time",
  "char_id": "character_id",
  "char_nm": "character_name",
  "due_gms": "deserved_gems",
  "env_sel": "choose_env",
  "gen_vid": "gen_video",
  "lck_lvl": "lock_level",
  "lck_med": "lock_level_media",
  "nxt_msg": "next_msgs",
  "ord_typ": "order_type",
  "prod_id": "product_id",
  "rc_stat": "recharge_status",
  "rnd_stl": "render_style",
  "rpt_typ": "report_type",
  "vip_lvl": "vip",
  "vox_dur": "voice_duration",
  "vox_url": "voice_url",
  "crncy_cd": "currency_code",
  "curr_sym": "currency_symbol",
  "hid_char": "hide_character",
  "kit_name": "cname",
  "kit_type": "ctype",
  "orig_txn": "original_transaction_id",
  "sess_cnt": "session_count",
  "thmb_url": "thumbnail_url",
  "gen_ptags": "gen_photo_tags",
  "perk_name": "gname",
  "perk_type": "gtype",
  "pln_msgid": "planned_msg_id",
  "change_kit": "change_clothing",
  "char_vchat": "character_video_chat"
};

var m2 = {
  "rp": "roleplay",
  "jwl": "gems",
  "msg": "message",
  "nkd": "noDress",
  "reg": "register",
  "stp": "undress",
  "sub": "subscription",
  "usr": "appUser",
  "vox": "voices",
  "conv": "conversation",
  "kits": "clothes",
  "perk": "gift",
  "chars": "characters",
  "rcOrd": "rechargeOrders",
  "subTx": "subscriptionTransactions",
  "aiConv": "aiChatConversation",
  "chrPrf": "characterProfile",
  "pltCfg": "platformConfig",
  "usrRpt": "appUserReport",
  "getKits": "getClothingConf",
  "getPerk": "getGiftConf",
  "recRole": "getRecommendRole",
  "udsRslt": "undressResult"
};
