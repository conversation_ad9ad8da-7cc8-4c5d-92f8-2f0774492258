package com.example.bushy

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger
import com.facebook.LoggingBehavior

class MainActivity : FlutterActivity() {
    private val CHANNEL = "facebook_sdk_channel"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            CHANNEL
        ).setMethodCallHandler { call, result ->
            when (call.method) {
                "initializeFacebookSDK" -> {
                    try {
                        val appId = call.argument<String>("appId")
                        val clientToken = call.argument<String>("clientToken")

                        FacebookSdk.setAutoInitEnabled(false)

                        if (appId != null && clientToken != null) {
                            println("初始化Facebook SDK appId:$appId clientToken:$clientToken")

                            FacebookSdk.setApplicationId(appId)
                            FacebookSdk.setClientToken(clientToken)
                            FacebookSdk.fullyInitialize();
                            // 使用 sdkInitialize 进行初始化
                            AppEventsLogger.activateApp(application)                            
                            // SDK 初始化完成后设置参数
                            FacebookSdk.setAdvertiserIDCollectionEnabled(true)
                            FacebookSdk.setAutoLogAppEventsEnabled(true)
                            FacebookSdk.setIsDebugEnabled(true)
                            FacebookSdk.addLoggingBehavior(LoggingBehavior.APP_EVENTS)
                            FacebookSdk.addLoggingBehavior(LoggingBehavior.REQUESTS)
                            FacebookSdk.addLoggingBehavior(LoggingBehavior.DEVELOPER_ERRORS)
                            
                            println("✅ Facebook SDK 初始化成功")
                            result.success("Facebook SDK initialized successfully")
                        } else {
                            result.error(
                                "INVALID_ARGUMENTS",
                                "App ID and Client Token are required",
                                null
                            )
                        }
                    } catch (e: Exception) {
                        result.error(
                            "INITIALIZATION_ERROR",
                            "Failed to initialize Facebook SDK: ${e.message}",
                            null
                        )
                    }
                }

                "isFacebookSDKInitialized" -> {
                    try {
                        val isInitialized = FacebookSdk.isInitialized()
                        result.success(isInitialized)
                    } catch (e: Exception) {
                        result.error(
                            "CHECK_ERROR",
                            "Failed to check Facebook SDK status: ${e.message}",
                            null
                        )
                    }
                }

                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
